# FlowCustomV1 快速开始指南

## 环境要求

### 开发环境
- **.NET 8.0 SDK** - [下载地址](https://dotnet.microsoft.com/download/dotnet/8.0)
- **Node.js 18+** - [下载地址](https://nodejs.org/)
- **Visual Studio 2022** 或 **VS Code** - 推荐的开发工具
- **Git** - 版本控制工具

### 可选工具
- **Docker Desktop** - 容器化部署
- **SQLite Browser** - 数据库查看工具
- **Postman** - API 测试工具

## 项目设置

### 1. 克隆项目
```bash
git clone <repository-url>
cd FlowCustomV1
```

### 2. 后端设置

#### 还原 NuGet 包
```bash
dotnet restore
```

#### 构建项目
```bash
dotnet build
```

#### 运行数据库迁移
```bash
cd src/FlowCustomV1.Api
dotnet ef database update
```

#### 启动 API 服务
```bash
dotnet run
```

API 服务将在 `https://localhost:7001` 启动

### 3. 前端设置

#### 安装依赖
```bash
cd frontend
npm install
```

#### 启动开发服务器
```bash
npm run dev
```

前端应用将在 `http://localhost:5173` 启动

## 项目结构说明

```
FlowCustomV1/
├── src/                           # 后端源代码
│   ├── FlowCustomV1.Core/         # 核心框架
│   ├── FlowCustomV1.SDK/          # 插件开发SDK
│   ├── FlowCustomV1.Data/         # 数据访问层
│   ├── FlowCustomV1.Engine/       # 工作流执行引擎
│   ├── FlowCustomV1.PluginHost/   # 插件宿主
│   └── FlowCustomV1.Api/          # Web API
├── plugins/                       # 插件目录
├── frontend/                      # React 前端应用
├── tests/                         # 测试项目
└── docs/                          # 文档
```

## 核心概念

### 1. 工作流定义 (WorkflowDefinition)
工作流是由节点和连接组成的有向图，定义了数据处理的流程。

### 2. 节点 (Node)
节点是工作流中的基本执行单元，每个节点都有输入端点和输出端点。

### 3. 插件 (Plugin)
插件是包含一个或多个节点类型的可加载模块，支持热插拔。

### 4. 执行引擎 (Engine)
负责解析工作流定义并按照依赖关系执行节点。

## 第一个工作流

### 1. 创建简单工作流

通过 API 创建一个简单的工作流：

```json
{
  "name": "Hello World Workflow",
  "description": "我的第一个工作流",
  "nodes": [
    {
      "id": "start",
      "name": "开始",
      "type": "StartNode",
      "pluginName": "Core",
      "position": { "x": 100, "y": 100 },
      "configuration": {},
      "inputEndpoints": [],
      "outputEndpoints": [
        {
          "id": "output",
          "name": "输出",
          "type": "any"
        }
      ]
    },
    {
      "id": "log",
      "name": "日志输出",
      "type": "LogNode",
      "pluginName": "Core",
      "position": { "x": 300, "y": 100 },
      "configuration": {
        "message": "Hello, World!"
      },
      "inputEndpoints": [
        {
          "id": "input",
          "name": "输入",
          "type": "any"
        }
      ],
      "outputEndpoints": [
        {
          "id": "output",
          "name": "输出",
          "type": "any"
        }
      ]
    },
    {
      "id": "end",
      "name": "结束",
      "type": "EndNode",
      "pluginName": "Core",
      "position": { "x": 500, "y": 100 },
      "configuration": {},
      "inputEndpoints": [
        {
          "id": "input",
          "name": "输入",
          "type": "any"
        }
      ],
      "outputEndpoints": []
    }
  ],
  "connections": [
    {
      "id": "conn1",
      "sourceNodeId": "start",
      "sourceEndpointId": "output",
      "targetNodeId": "log",
      "targetEndpointId": "input"
    },
    {
      "id": "conn2",
      "sourceNodeId": "log",
      "sourceEndpointId": "output",
      "targetNodeId": "end",
      "targetEndpointId": "input"
    }
  ]
}
```

### 2. 执行工作流

```bash
curl -X POST "https://localhost:7001/api/workflows/{workflow-id}/execute" \
  -H "Content-Type: application/json" \
  -d '{}'
```

## 开发自定义插件

### 1. 创建插件项目

```bash
dotnet new classlib -n MyCustomPlugin
cd MyCustomPlugin
dotnet add package FlowCustomV1.SDK
```

### 2. 实现插件主类

```csharp
using FlowCustomV1.SDK;
using FlowCustomV1.SDK.Attributes;
using FlowCustomV1.Core.Models;

[PluginInfo("MyCustomPlugin", "我的自定义插件", "1.0.0",
    Description = "这是一个示例插件",
    Author = "Your Name",
    Category = "Custom")]
public class MyCustomPlugin : BasePlugin
{
    public override PluginInfo PluginInfo => new()
    {
        Name = "MyCustomPlugin",
        DisplayName = "我的自定义插件",
        Version = "1.0.0",
        Description = "这是一个示例插件",
        Author = "Your Name"
    };

    public override async Task<INodeExecutor?> CreateNodeExecutorAsync(
        string nodeType, 
        CancellationToken cancellationToken = default)
    {
        return nodeType switch
        {
            "MyCustomNode" => new MyCustomNodeExecutor(GetRequiredService<ILogger<MyCustomNodeExecutor>>()),
            _ => null
        };
    }

    public override async Task<IEnumerable<NodeTypeDefinition>> GetNodeTypeDefinitionsAsync(
        CancellationToken cancellationToken = default)
    {
        return new[]
        {
            NodeTypeDefinitionHelper.CreateFromType(typeof(MyCustomNodeExecutor))
        };
    }
}
```

### 3. 实现节点执行器

```csharp
using FlowCustomV1.SDK;
using FlowCustomV1.SDK.Attributes;
using FlowCustomV1.Core.Interfaces;
using FlowCustomV1.Core.Models;

[NodeType("MyCustomNode", "我的自定义节点",
    Description = "这是一个示例节点",
    Category = "Custom",
    Icon = "🔧")]
public class MyCustomNodeExecutor : BaseNodeExecutor
{
    public override string NodeType => "MyCustomNode";

    public MyCustomNodeExecutor(ILogger<MyCustomNodeExecutor> logger) : base(logger)
    {
    }

    protected override async Task<NodeExecutionResult> ExecuteCoreAsync(
        NodeExecutionContext context, 
        CancellationToken cancellationToken)
    {
        // 获取配置参数
        var message = GetConfigurationValue<string>(context.Node.Configuration, "message", "Hello");
        
        // 获取输入数据
        var input = GetInputValue<string>(context.InputData, "input", "");

        // 执行业务逻辑
        var result = $"{message}: {input}";
        
        // 记录日志
        context.Logger.LogInformation("执行自定义节点: {Result}", result);

        // 返回结果
        var outputData = new Dictionary<string, object>
        {
            ["output"] = result
        };

        return NodeExecutionResult.Success(outputData);
    }

    public override NodeTypeDefinition GetNodeTypeDefinition()
    {
        return new NodeTypeDefinition
        {
            TypeName = "MyCustomNode",
            DisplayName = "我的自定义节点",
            Description = "这是一个示例节点",
            Category = "Custom",
            Icon = "🔧",
            ExecutorTypeName = typeof(MyCustomNodeExecutor).FullName!,
            InputEndpoints = new List<EndpointDefinition>
            {
                new()
                {
                    Name = "input",
                    DisplayName = "输入",
                    Type = "string",
                    IsRequired = false
                }
            },
            OutputEndpoints = new List<EndpointDefinition>
            {
                new()
                {
                    Name = "output",
                    DisplayName = "输出",
                    Type = "string"
                }
            },
            ConfigurationSchema = new Dictionary<string, object>
            {
                ["message"] = new Dictionary<string, object>
                {
                    ["type"] = "string",
                    ["displayName"] = "消息",
                    ["description"] = "要输出的消息",
                    ["defaultValue"] = "Hello",
                    ["required"] = false
                }
            }
        };
    }
}
```

### 4. 构建和部署插件

```bash
dotnet build
# 将生成的 DLL 复制到 plugins 目录
cp bin/Debug/net8.0/MyCustomPlugin.dll ../plugins/
```

## 常用 API 接口

### 工作流管理
- `GET /api/workflows` - 获取所有工作流
- `POST /api/workflows` - 创建工作流
- `GET /api/workflows/{id}` - 获取工作流详情
- `PUT /api/workflows/{id}` - 更新工作流
- `DELETE /api/workflows/{id}` - 删除工作流

### 执行管理
- `POST /api/workflows/{id}/execute` - 执行工作流
- `GET /api/executions` - 获取执行记录
- `GET /api/executions/{id}` - 获取执行详情
- `POST /api/executions/{id}/cancel` - 取消执行

### 插件管理
- `GET /api/plugins` - 获取插件列表
- `GET /api/plugins/{name}/node-types` - 获取插件节点类型

## 调试技巧

### 1. 启用详细日志
在 `appsettings.Development.json` 中设置：
```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Debug",
      "FlowCustomV1": "Trace"
    }
  }
}
```

### 2. 使用断点调试
在 Visual Studio 中设置断点，然后按 F5 启动调试。

### 3. 查看执行日志
通过 API 获取执行日志：
```bash
curl "https://localhost:7001/api/executions/{execution-id}/logs"
```

## 常见问题

### Q: 插件加载失败怎么办？
A: 检查插件 DLL 是否在正确的目录，确保依赖项完整，查看日志获取详细错误信息。

### Q: 工作流执行失败怎么办？
A: 检查节点配置是否正确，查看执行日志，确认插件是否正常加载。

### Q: 前端无法连接后端怎么办？
A: 确认后端服务正在运行，检查端口配置，确认 CORS 设置正确。

## 下一步

1. 阅读 [插件开发指南](docs/plugin-development.md)
2. 查看 [API 文档](docs/api-reference.md)
3. 学习 [最佳实践](docs/best-practices.md)
4. 参与 [社区讨论](https://github.com/your-repo/discussions)

## 获取帮助

- 📖 [完整文档](docs/)
- 🐛 [问题反馈](https://github.com/your-repo/issues)
- 💬 [社区讨论](https://github.com/your-repo/discussions)
- 📧 [联系我们](mailto:<EMAIL>)
