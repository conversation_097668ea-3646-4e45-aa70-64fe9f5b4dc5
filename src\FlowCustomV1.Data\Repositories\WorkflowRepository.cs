using FlowCustomV1.Core.Interfaces;
using FlowCustomV1.Core.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace FlowCustomV1.Data.Repositories;

/// <summary>
/// 工作流仓储实现
/// </summary>
public class WorkflowRepository : IWorkflowRepository
{
    private readonly FlowCustomDbContext _context;
    private readonly ILogger<WorkflowRepository> _logger;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="context">数据库上下文</param>
    /// <param name="logger">日志记录器</param>
    public WorkflowRepository(FlowCustomDbContext context, ILogger<WorkflowRepository> logger)
    {
        _context = context;
        _logger = logger;
    }

    /// <summary>
    /// 获取所有工作流定义
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>工作流定义列表</returns>
    public async Task<IEnumerable<WorkflowDefinition>> GetAllAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            return await _context.WorkflowDefinitions
                .OrderBy(w => w.Name)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取所有工作流定义失败");
            throw;
        }
    }

    /// <summary>
    /// 根据ID获取工作流定义
    /// </summary>
    /// <param name="id">工作流ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>工作流定义</returns>
    public async Task<WorkflowDefinition?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _context.WorkflowDefinitions
                .FirstOrDefaultAsync(w => w.Id == id, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据ID获取工作流定义失败: {WorkflowId}", id);
            throw;
        }
    }

    /// <summary>
    /// 根据名称获取工作流定义
    /// </summary>
    /// <param name="name">工作流名称</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>工作流定义</returns>
    public async Task<WorkflowDefinition?> GetByNameAsync(string name, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _context.WorkflowDefinitions
                .FirstOrDefaultAsync(w => w.Name == name, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据名称获取工作流定义失败: {WorkflowName}", name);
            throw;
        }
    }

    /// <summary>
    /// 创建工作流定义
    /// </summary>
    /// <param name="workflow">工作流定义</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>创建的工作流定义</returns>
    public async Task<WorkflowDefinition> CreateAsync(WorkflowDefinition workflow, CancellationToken cancellationToken = default)
    {
        try
        {
            workflow.Id = Guid.NewGuid();
            workflow.CreatedAt = DateTime.UtcNow;
            workflow.UpdatedAt = DateTime.UtcNow;

            _context.WorkflowDefinitions.Add(workflow);
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("创建工作流定义成功: {WorkflowId} - {WorkflowName}", workflow.Id, workflow.Name);
            return workflow;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建工作流定义失败: {WorkflowName}", workflow.Name);
            throw;
        }
    }

    /// <summary>
    /// 更新工作流定义
    /// </summary>
    /// <param name="workflow">工作流定义</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>更新的工作流定义</returns>
    public async Task<WorkflowDefinition> UpdateAsync(WorkflowDefinition workflow, CancellationToken cancellationToken = default)
    {
        try
        {
            workflow.UpdatedAt = DateTime.UtcNow;

            _context.WorkflowDefinitions.Update(workflow);
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("更新工作流定义成功: {WorkflowId} - {WorkflowName}", workflow.Id, workflow.Name);
            return workflow;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新工作流定义失败: {WorkflowId} - {WorkflowName}", workflow.Id, workflow.Name);
            throw;
        }
    }

    /// <summary>
    /// 删除工作流定义
    /// </summary>
    /// <param name="id">工作流ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否删除成功</returns>
    public async Task<bool> DeleteAsync(Guid id, CancellationToken cancellationToken = default)
    {
        try
        {
            var workflow = await _context.WorkflowDefinitions
                .FirstOrDefaultAsync(w => w.Id == id, cancellationToken);

            if (workflow == null)
            {
                _logger.LogWarning("要删除的工作流定义不存在: {WorkflowId}", id);
                return false;
            }

            _context.WorkflowDefinitions.Remove(workflow);
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("删除工作流定义成功: {WorkflowId} - {WorkflowName}", id, workflow.Name);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除工作流定义失败: {WorkflowId}", id);
            throw;
        }
    }

    /// <summary>
    /// 检查工作流名称是否存在
    /// </summary>
    /// <param name="name">工作流名称</param>
    /// <param name="excludeId">排除的工作流ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否存在</returns>
    public async Task<bool> ExistsAsync(string name, Guid? excludeId = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var query = _context.WorkflowDefinitions.Where(w => w.Name == name);

            if (excludeId.HasValue)
            {
                query = query.Where(w => w.Id != excludeId.Value);
            }

            return await query.AnyAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查工作流名称是否存在失败: {WorkflowName}", name);
            throw;
        }
    }

    /// <summary>
    /// 获取启用的工作流定义
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>启用的工作流定义列表</returns>
    public async Task<IEnumerable<WorkflowDefinition>> GetEnabledAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            return await _context.WorkflowDefinitions
                .Where(w => w.IsEnabled)
                .OrderBy(w => w.Name)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取启用的工作流定义失败");
            throw;
        }
    }
}
