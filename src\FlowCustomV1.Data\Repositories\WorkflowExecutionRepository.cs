using FlowCustomV1.Core.Interfaces;
using FlowCustomV1.Core.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace FlowCustomV1.Data.Repositories;

/// <summary>
/// 工作流执行仓储实现
/// </summary>
public class WorkflowExecutionRepository : IWorkflowExecutionRepository
{
    private readonly FlowCustomDbContext _context;
    private readonly ILogger<WorkflowExecutionRepository> _logger;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="context">数据库上下文</param>
    /// <param name="logger">日志记录器</param>
    public WorkflowExecutionRepository(FlowCustomDbContext context, ILogger<WorkflowExecutionRepository> logger)
    {
        _context = context;
        _logger = logger;
    }

    /// <summary>
    /// 获取所有工作流执行记录
    /// </summary>
    public async Task<(IEnumerable<WorkflowExecution> Items, int TotalCount)> GetAllAsync(
        Guid? workflowId = null,
        WorkflowExecutionStatus? status = null,
        int pageIndex = 0,
        int pageSize = 20,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var query = _context.WorkflowExecutions
                .Include(e => e.WorkflowDefinition)
                .AsQueryable();

            if (workflowId.HasValue)
                query = query.Where(e => e.WorkflowDefinitionId == workflowId.Value);

            if (status.HasValue)
                query = query.Where(e => e.Status == status.Value);

            var totalCount = await query.CountAsync(cancellationToken);

            var items = await query
                .OrderByDescending(e => e.CreatedAt)
                .Skip(pageIndex * pageSize)
                .Take(pageSize)
                .ToListAsync(cancellationToken);

            return (items, totalCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取工作流执行记录失败");
            throw;
        }
    }

    /// <summary>
    /// 根据ID获取工作流执行记录
    /// </summary>
    public async Task<WorkflowExecution?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        try
        {
            return await _context.WorkflowExecutions
                .Include(e => e.WorkflowDefinition)
                .Include(e => e.NodeExecutions)
                .Include(e => e.Logs)
                .FirstOrDefaultAsync(e => e.Id == id, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "根据ID获取工作流执行记录失败: {ExecutionId}", id);
            throw;
        }
    }

    /// <summary>
    /// 创建工作流执行记录
    /// </summary>
    public async Task<WorkflowExecution> CreateAsync(WorkflowExecution execution, CancellationToken cancellationToken = default)
    {
        try
        {
            execution.Id = Guid.NewGuid();
            execution.CreatedAt = DateTime.UtcNow;

            _context.WorkflowExecutions.Add(execution);
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("创建工作流执行记录成功: {ExecutionId}", execution.Id);
            return execution;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建工作流执行记录失败");
            throw;
        }
    }

    /// <summary>
    /// 更新工作流执行记录
    /// </summary>
    public async Task<WorkflowExecution> UpdateAsync(WorkflowExecution execution, CancellationToken cancellationToken = default)
    {
        try
        {
            _context.WorkflowExecutions.Update(execution);
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("更新工作流执行记录成功: {ExecutionId}", execution.Id);
            return execution;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新工作流执行记录失败: {ExecutionId}", execution.Id);
            throw;
        }
    }

    /// <summary>
    /// 删除工作流执行记录
    /// </summary>
    public async Task<bool> DeleteAsync(Guid id, CancellationToken cancellationToken = default)
    {
        try
        {
            var execution = await _context.WorkflowExecutions
                .FirstOrDefaultAsync(e => e.Id == id, cancellationToken);

            if (execution == null)
            {
                _logger.LogWarning("要删除的工作流执行记录不存在: {ExecutionId}", id);
                return false;
            }

            _context.WorkflowExecutions.Remove(execution);
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("删除工作流执行记录成功: {ExecutionId}", id);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除工作流执行记录失败: {ExecutionId}", id);
            throw;
        }
    }

    /// <summary>
    /// 获取正在运行的工作流执行记录
    /// </summary>
    public async Task<IEnumerable<WorkflowExecution>> GetRunningAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            return await _context.WorkflowExecutions
                .Include(e => e.WorkflowDefinition)
                .Where(e => e.Status == WorkflowExecutionStatus.Running || e.Status == WorkflowExecutionStatus.Paused)
                .OrderBy(e => e.StartedAt)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取正在运行的工作流执行记录失败");
            throw;
        }
    }

    /// <summary>
    /// 添加节点执行记录
    /// </summary>
    public async Task<NodeExecution> AddNodeExecutionAsync(NodeExecution nodeExecution, CancellationToken cancellationToken = default)
    {
        try
        {
            nodeExecution.Id = Guid.NewGuid();
            _context.NodeExecutions.Add(nodeExecution);
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogDebug("添加节点执行记录成功: {NodeExecutionId}", nodeExecution.Id);
            return nodeExecution;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "添加节点执行记录失败");
            throw;
        }
    }

    /// <summary>
    /// 更新节点执行记录
    /// </summary>
    public async Task<NodeExecution> UpdateNodeExecutionAsync(NodeExecution nodeExecution, CancellationToken cancellationToken = default)
    {
        try
        {
            _context.NodeExecutions.Update(nodeExecution);
            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogDebug("更新节点执行记录成功: {NodeExecutionId}", nodeExecution.Id);
            return nodeExecution;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新节点执行记录失败: {NodeExecutionId}", nodeExecution.Id);
            throw;
        }
    }

    /// <summary>
    /// 添加执行日志
    /// </summary>
    public async Task<ExecutionLog> AddLogAsync(ExecutionLog log, CancellationToken cancellationToken = default)
    {
        try
        {
            log.Id = Guid.NewGuid();
            log.Timestamp = DateTime.UtcNow;
            _context.ExecutionLogs.Add(log);
            await _context.SaveChangesAsync(cancellationToken);

            return log;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "添加执行日志失败");
            throw;
        }
    }

    /// <summary>
    /// 获取执行日志
    /// </summary>
    public async Task<IEnumerable<ExecutionLog>> GetLogsAsync(Guid executionId, Guid? nodeExecutionId = null, CancellationToken cancellationToken = default)
    {
        try
        {
            var query = _context.ExecutionLogs
                .Where(l => l.WorkflowExecutionId == executionId);

            if (nodeExecutionId.HasValue)
                query = query.Where(l => l.NodeExecutionId == nodeExecutionId.Value);

            return await query
                .OrderBy(l => l.Timestamp)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取执行日志失败: {ExecutionId}", executionId);
            throw;
        }
    }
}
