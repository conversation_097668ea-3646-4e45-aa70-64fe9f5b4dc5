using FlowCustomV1.Core.Interfaces;
using Microsoft.Extensions.DependencyInjection;

namespace FlowCustomV1.Core.Extensions;

/// <summary>
/// 服务集合扩展方法
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// 添加 FlowCustom 核心服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddFlowCustomCore(this IServiceCollection services)
    {
        // 注册核心接口的默认实现将在其他项目中完成
        return services;
    }

    /// <summary>
    /// 添加工作流服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddWorkflowServices(this IServiceCollection services)
    {
        // 工作流相关服务注册
        return services;
    }

    /// <summary>
    /// 添加插件服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddPluginServices(this IServiceCollection services)
    {
        // 插件相关服务注册
        return services;
    }
}
