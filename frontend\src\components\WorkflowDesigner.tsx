import React, { use<PERSON>allback, useState } from 'react';
import React<PERSON><PERSON>, {
  Node,
  Edge,
  addEdge,
  Connection,
  useNodesState,
  useEdgesState,
  Controls,
  MiniMap,
  Background,
  BackgroundVariant,
} from 'reactflow';
import 'reactflow/dist/style.css';
import { But<PERSON>, Card, Drawer, Form, Input, Select, Space, message } from 'antd';
import { PlayCircleOutlined, SaveOutlined, SettingOutlined } from '@ant-design/icons';

const { Option } = Select;

interface WorkflowNode extends Node {
  data: {
    label: string;
    type: string;
    pluginName: string;
    configuration: Record<string, any>;
  };
}

interface WorkflowDesignerProps {
  workflowId?: string;
  onSave?: (workflow: any) => void;
  onExecute?: (workflowId: string) => void;
}

const initialNodes: WorkflowNode[] = [
  {
    id: '1',
    type: 'input',
    data: { 
      label: '开始节点',
      type: 'StartNode',
      pluginName: 'Core',
      configuration: {}
    },
    position: { x: 250, y: 25 },
  },
];

const initialEdges: Edge[] = [];

const nodeTypes = [
  { value: 'StartNode', label: '开始节点', plugin: 'Core' },
  { value: 'EndNode', label: '结束节点', plugin: 'Core' },
  { value: 'LogNode', label: '日志节点', plugin: 'Core' },
  { value: 'ConditionNode', label: '条件节点', plugin: 'Core' },
  { value: 'HttpRequestNode', label: 'HTTP请求', plugin: 'Http' },
  { value: 'DataTransformNode', label: '数据转换', plugin: 'Data' },
];

export const WorkflowDesigner: React.FC<WorkflowDesignerProps> = ({
  workflowId,
  onSave,
  onExecute,
}) => {
  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);
  const [selectedNode, setSelectedNode] = useState<WorkflowNode | null>(null);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [form] = Form.useForm();

  const onConnect = useCallback(
    (params: Connection) => setEdges((eds) => addEdge(params, eds)),
    [setEdges]
  );

  const onNodeClick = useCallback((event: React.MouseEvent, node: WorkflowNode) => {
    setSelectedNode(node);
    form.setFieldsValue({
      label: node.data.label,
      type: node.data.type,
      pluginName: node.data.pluginName,
      ...node.data.configuration,
    });
    setDrawerVisible(true);
  }, [form]);

  const addNode = useCallback((nodeType: string) => {
    const nodeTypeInfo = nodeTypes.find(nt => nt.value === nodeType);
    if (!nodeTypeInfo) return;

    const newNode: WorkflowNode = {
      id: `${nodes.length + 1}`,
      type: nodeType === 'StartNode' ? 'input' : nodeType === 'EndNode' ? 'output' : 'default',
      data: {
        label: nodeTypeInfo.label,
        type: nodeType,
        pluginName: nodeTypeInfo.plugin,
        configuration: {},
      },
      position: { x: Math.random() * 400, y: Math.random() * 400 },
    };

    setNodes((nds) => nds.concat(newNode));
  }, [nodes.length, setNodes]);

  const updateNode = useCallback(() => {
    if (!selectedNode) return;

    const values = form.getFieldsValue();
    const updatedNode: WorkflowNode = {
      ...selectedNode,
      data: {
        ...selectedNode.data,
        label: values.label,
        type: values.type,
        pluginName: values.pluginName,
        configuration: {
          ...Object.keys(values).reduce((config, key) => {
            if (!['label', 'type', 'pluginName'].includes(key)) {
              config[key] = values[key];
            }
            return config;
          }, {} as Record<string, any>),
        },
      },
    };

    setNodes((nds) =>
      nds.map((node) => (node.id === selectedNode.id ? updatedNode : node))
    );

    setDrawerVisible(false);
    message.success('节点更新成功');
  }, [selectedNode, form, setNodes]);

  const saveWorkflow = useCallback(() => {
    const workflow = {
      id: workflowId,
      name: '工作流',
      description: '通过设计器创建的工作流',
      nodes: nodes.map(node => ({
        id: node.id,
        name: node.data.label,
        type: node.data.type,
        pluginName: node.data.pluginName,
        position: node.position,
        configuration: node.data.configuration,
        inputEndpoints: [],
        outputEndpoints: [],
      })),
      connections: edges.map(edge => ({
        id: edge.id,
        sourceNodeId: edge.source,
        sourceEndpointId: 'output',
        targetNodeId: edge.target,
        targetEndpointId: 'input',
      })),
      variables: {},
      configuration: {
        timeoutSeconds: 300,
        maxRetryCount: 3,
        enableParallelExecution: true,
        errorHandling: 'StopOnError',
        logLevel: 'Information',
      },
    };

    onSave?.(workflow);
    message.success('工作流保存成功');
  }, [nodes, edges, workflowId, onSave]);

  const executeWorkflow = useCallback(() => {
    if (!workflowId) {
      message.error('请先保存工作流');
      return;
    }

    onExecute?.(workflowId);
    message.info('工作流执行已启动');
  }, [workflowId, onExecute]);

  return (
    <div style={{ width: '100%', height: '600px' }}>
      <Card
        title="工作流设计器"
        extra={
          <Space>
            <Select
              placeholder="添加节点"
              style={{ width: 150 }}
              onSelect={addNode}
              value={undefined}
            >
              {nodeTypes.map(nodeType => (
                <Option key={nodeType.value} value={nodeType.value}>
                  {nodeType.label}
                </Option>
              ))}
            </Select>
            <Button
              type="primary"
              icon={<SaveOutlined />}
              onClick={saveWorkflow}
            >
              保存
            </Button>
            <Button
              type="default"
              icon={<PlayCircleOutlined />}
              onClick={executeWorkflow}
              disabled={!workflowId}
            >
              执行
            </Button>
          </Space>
        }
        bodyStyle={{ padding: 0 }}
      >
        <ReactFlow
          nodes={nodes}
          edges={edges}
          onNodesChange={onNodesChange}
          onEdgesChange={onEdgesChange}
          onConnect={onConnect}
          onNodeClick={onNodeClick}
          fitView
        >
          <Controls />
          <MiniMap />
          <Background variant={BackgroundVariant.Dots} gap={12} size={1} />
        </ReactFlow>
      </Card>

      <Drawer
        title={
          <Space>
            <SettingOutlined />
            节点配置
          </Space>
        }
        width={400}
        open={drawerVisible}
        onClose={() => setDrawerVisible(false)}
        extra={
          <Space>
            <Button onClick={() => setDrawerVisible(false)}>取消</Button>
            <Button type="primary" onClick={updateNode}>
              确定
            </Button>
          </Space>
        }
      >
        {selectedNode && (
          <Form
            form={form}
            layout="vertical"
            initialValues={{
              label: selectedNode.data.label,
              type: selectedNode.data.type,
              pluginName: selectedNode.data.pluginName,
              ...selectedNode.data.configuration,
            }}
          >
            <Form.Item
              name="label"
              label="节点名称"
              rules={[{ required: true, message: '请输入节点名称' }]}
            >
              <Input />
            </Form.Item>

            <Form.Item
              name="type"
              label="节点类型"
              rules={[{ required: true, message: '请选择节点类型' }]}
            >
              <Select>
                {nodeTypes.map(nodeType => (
                  <Option key={nodeType.value} value={nodeType.value}>
                    {nodeType.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item
              name="pluginName"
              label="插件名称"
              rules={[{ required: true, message: '请输入插件名称' }]}
            >
              <Input />
            </Form.Item>

            {/* 根据节点类型显示不同的配置项 */}
            {selectedNode.data.type === 'LogNode' && (
              <Form.Item name="message" label="日志消息">
                <Input.TextArea rows={3} placeholder="请输入要记录的日志消息" />
              </Form.Item>
            )}

            {selectedNode.data.type === 'HttpRequestNode' && (
              <>
                <Form.Item name="url" label="请求URL">
                  <Input placeholder="https://api.example.com/data" />
                </Form.Item>
                <Form.Item name="method" label="请求方法">
                  <Select defaultValue="GET">
                    <Option value="GET">GET</Option>
                    <Option value="POST">POST</Option>
                    <Option value="PUT">PUT</Option>
                    <Option value="DELETE">DELETE</Option>
                  </Select>
                </Form.Item>
                <Form.Item name="headers" label="请求头">
                  <Input.TextArea
                    rows={3}
                    placeholder='{"Content-Type": "application/json"}'
                  />
                </Form.Item>
              </>
            )}

            {selectedNode.data.type === 'ConditionNode' && (
              <>
                <Form.Item name="condition" label="条件表达式">
                  <Input placeholder="例如: input.value > 10" />
                </Form.Item>
                <Form.Item name="trueValue" label="条件为真时的值">
                  <Input />
                </Form.Item>
                <Form.Item name="falseValue" label="条件为假时的值">
                  <Input />
                </Form.Item>
              </>
            )}
          </Form>
        )}
      </Drawer>
    </div>
  );
};
