using FlowCustomV1.Core.Models;

namespace FlowCustomV1.Core.Interfaces;

/// <summary>
/// 插件管理器接口
/// </summary>
public interface IPluginManager
{
    /// <summary>
    /// 加载插件
    /// </summary>
    /// <param name="pluginPath">插件路径</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>插件定义</returns>
    Task<PluginDefinition> LoadPluginAsync(string pluginPath, CancellationToken cancellationToken = default);

    /// <summary>
    /// 卸载插件
    /// </summary>
    /// <param name="pluginName">插件名称</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否卸载成功</returns>
    Task<bool> UnloadPluginAsync(string pluginName, CancellationToken cancellationToken = default);

    /// <summary>
    /// 重新加载插件
    /// </summary>
    /// <param name="pluginName">插件名称</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否重新加载成功</returns>
    Task<bool> ReloadPluginAsync(string pluginName, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取已加载的插件列表
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>已加载的插件列表</returns>
    Task<IEnumerable<PluginDefinition>> GetLoadedPluginsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取插件定义
    /// </summary>
    /// <param name="pluginName">插件名称</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>插件定义</returns>
    Task<PluginDefinition?> GetPluginAsync(string pluginName, CancellationToken cancellationToken = default);

    /// <summary>
    /// 检查插件是否已加载
    /// </summary>
    /// <param name="pluginName">插件名称</param>
    /// <returns>是否已加载</returns>
    bool IsPluginLoaded(string pluginName);

    /// <summary>
    /// 创建节点执行器
    /// </summary>
    /// <param name="pluginName">插件名称</param>
    /// <param name="nodeType">节点类型</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>节点执行器</returns>
    Task<INodeExecutor?> CreateNodeExecutorAsync(string pluginName, string nodeType, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取节点类型定义
    /// </summary>
    /// <param name="pluginName">插件名称</param>
    /// <param name="nodeType">节点类型</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>节点类型定义</returns>
    Task<NodeTypeDefinition?> GetNodeTypeDefinitionAsync(string pluginName, string nodeType, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取所有节点类型定义
    /// </summary>
    /// <param name="category">类别（可选）</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>节点类型定义列表</returns>
    Task<IEnumerable<(PluginDefinition Plugin, NodeTypeDefinition NodeType)>> GetAllNodeTypeDefinitionsAsync(
        string? category = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 扫描插件目录
    /// </summary>
    /// <param name="pluginDirectory">插件目录</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发现的插件路径列表</returns>
    Task<IEnumerable<string>> ScanPluginDirectoryAsync(string pluginDirectory, CancellationToken cancellationToken = default);

    /// <summary>
    /// 验证插件
    /// </summary>
    /// <param name="pluginPath">插件路径</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证结果</returns>
    Task<PluginValidationResult> ValidatePluginAsync(string pluginPath, CancellationToken cancellationToken = default);

    /// <summary>
    /// 插件加载事件
    /// </summary>
    event EventHandler<PluginLoadedEventArgs>? PluginLoaded;

    /// <summary>
    /// 插件卸载事件
    /// </summary>
    event EventHandler<PluginUnloadedEventArgs>? PluginUnloaded;

    /// <summary>
    /// 插件加载失败事件
    /// </summary>
    event EventHandler<PluginLoadFailedEventArgs>? PluginLoadFailed;
}

/// <summary>
/// 插件验证结果
/// </summary>
public class PluginValidationResult
{
    /// <summary>
    /// 是否有效
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// 错误列表
    /// </summary>
    public List<string> Errors { get; set; } = new();

    /// <summary>
    /// 警告列表
    /// </summary>
    public List<string> Warnings { get; set; } = new();

    /// <summary>
    /// 插件信息
    /// </summary>
    public PluginInfo? PluginInfo { get; set; }
}

/// <summary>
/// 插件信息
/// </summary>
public class PluginInfo
{
    /// <summary>
    /// 插件名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 插件版本
    /// </summary>
    public string Version { get; set; } = string.Empty;

    /// <summary>
    /// 插件描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 插件作者
    /// </summary>
    public string? Author { get; set; }

    /// <summary>
    /// 程序集路径
    /// </summary>
    public string AssemblyPath { get; set; } = string.Empty;

    /// <summary>
    /// 主类型名称
    /// </summary>
    public string MainTypeName { get; set; } = string.Empty;

    /// <summary>
    /// 依赖项
    /// </summary>
    public List<PluginDependency> Dependencies { get; set; } = new();

    /// <summary>
    /// 节点类型
    /// </summary>
    public List<NodeTypeDefinition> NodeTypes { get; set; } = new();
}

/// <summary>
/// 插件加载事件参数
/// </summary>
public class PluginLoadedEventArgs : EventArgs
{
    /// <summary>
    /// 插件定义
    /// </summary>
    public PluginDefinition Plugin { get; set; } = null!;

    /// <summary>
    /// 加载时间
    /// </summary>
    public DateTime LoadedAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 插件卸载事件参数
/// </summary>
public class PluginUnloadedEventArgs : EventArgs
{
    /// <summary>
    /// 插件名称
    /// </summary>
    public string PluginName { get; set; } = string.Empty;

    /// <summary>
    /// 卸载时间
    /// </summary>
    public DateTime UnloadedAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 插件加载失败事件参数
/// </summary>
public class PluginLoadFailedEventArgs : EventArgs
{
    /// <summary>
    /// 插件路径
    /// </summary>
    public string PluginPath { get; set; } = string.Empty;

    /// <summary>
    /// 错误信息
    /// </summary>
    public string ErrorMessage { get; set; } = string.Empty;

    /// <summary>
    /// 异常
    /// </summary>
    public Exception? Exception { get; set; }

    /// <summary>
    /// 失败时间
    /// </summary>
    public DateTime FailedAt { get; set; } = DateTime.UtcNow;
}
