using FlowCustomV1.Core.Interfaces;
using FlowCustomV1.Core.Models;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace FlowCustomV1.SDK;

/// <summary>
/// 插件基类
/// </summary>
public abstract class BasePlugin : IPlugin
{
    /// <summary>
    /// 插件信息
    /// </summary>
    public abstract PluginInfo PluginInfo { get; }

    /// <summary>
    /// 服务提供者
    /// </summary>
    protected IServiceProvider? ServiceProvider { get; private set; }

    /// <summary>
    /// 日志记录器
    /// </summary>
    protected ILogger? Logger { get; private set; }

    /// <summary>
    /// 初始化插件
    /// </summary>
    /// <param name="serviceProvider">服务提供者</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>初始化任务</returns>
    public virtual Task InitializeAsync(IServiceProvider serviceProvider, CancellationToken cancellationToken = default)
    {
        ServiceProvider = serviceProvider;
        Logger = serviceProvider.GetService<ILogger<BasePlugin>>();
        
        Logger?.LogInformation("插件初始化: {PluginName} v{Version}", PluginInfo.Name, PluginInfo.Version);
        
        return OnInitializeAsync(cancellationToken);
    }

    /// <summary>
    /// 启动插件
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>启动任务</returns>
    public virtual Task StartAsync(CancellationToken cancellationToken = default)
    {
        Logger?.LogInformation("插件启动: {PluginName}", PluginInfo.Name);
        return OnStartAsync(cancellationToken);
    }

    /// <summary>
    /// 停止插件
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>停止任务</returns>
    public virtual Task StopAsync(CancellationToken cancellationToken = default)
    {
        Logger?.LogInformation("插件停止: {PluginName}", PluginInfo.Name);
        return OnStopAsync(cancellationToken);
    }

    /// <summary>
    /// 释放插件资源
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>释放任务</returns>
    public virtual Task DisposeAsync(CancellationToken cancellationToken = default)
    {
        Logger?.LogInformation("插件释放: {PluginName}", PluginInfo.Name);
        return OnDisposeAsync(cancellationToken);
    }

    /// <summary>
    /// 创建节点执行器
    /// </summary>
    /// <param name="nodeType">节点类型</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>节点执行器</returns>
    public abstract Task<INodeExecutor?> CreateNodeExecutorAsync(string nodeType, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取节点类型定义列表
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>节点类型定义列表</returns>
    public abstract Task<IEnumerable<NodeTypeDefinition>> GetNodeTypeDefinitionsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 配置服务
    /// </summary>
    /// <param name="services">服务集合</param>
    public virtual void ConfigureServices(IServiceCollection services)
    {
        // 子类可以重写此方法来注册自定义服务
    }

    /// <summary>
    /// 验证插件配置
    /// </summary>
    /// <param name="configuration">插件配置</param>
    /// <returns>验证结果</returns>
    public virtual Task<PluginValidationResult> ValidateConfigurationAsync(Dictionary<string, object> configuration)
    {
        return Task.FromResult(new PluginValidationResult { IsValid = true });
    }

    /// <summary>
    /// 获取插件健康状态
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>健康状态</returns>
    public virtual Task<PluginHealthStatus> GetHealthStatusAsync(CancellationToken cancellationToken = default)
    {
        return Task.FromResult(new PluginHealthStatus
        {
            IsHealthy = true,
            Status = "Healthy",
            LastChecked = DateTime.UtcNow
        });
    }

    /// <summary>
    /// 插件初始化时调用（子类重写）
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>初始化任务</returns>
    protected virtual Task OnInitializeAsync(CancellationToken cancellationToken)
    {
        return Task.CompletedTask;
    }

    /// <summary>
    /// 插件启动时调用（子类重写）
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>启动任务</returns>
    protected virtual Task OnStartAsync(CancellationToken cancellationToken)
    {
        return Task.CompletedTask;
    }

    /// <summary>
    /// 插件停止时调用（子类重写）
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>停止任务</returns>
    protected virtual Task OnStopAsync(CancellationToken cancellationToken)
    {
        return Task.CompletedTask;
    }

    /// <summary>
    /// 插件释放时调用（子类重写）
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>释放任务</returns>
    protected virtual Task OnDisposeAsync(CancellationToken cancellationToken)
    {
        return Task.CompletedTask;
    }

    /// <summary>
    /// 获取服务
    /// </summary>
    /// <typeparam name="T">服务类型</typeparam>
    /// <returns>服务实例</returns>
    protected T? GetService<T>() where T : class
    {
        return ServiceProvider?.GetService<T>();
    }

    /// <summary>
    /// 获取必需服务
    /// </summary>
    /// <typeparam name="T">服务类型</typeparam>
    /// <returns>服务实例</returns>
    protected T GetRequiredService<T>() where T : class
    {
        if (ServiceProvider == null)
            throw new InvalidOperationException("插件尚未初始化");

        return ServiceProvider.GetRequiredService<T>();
    }
}

/// <summary>
/// 插件接口
/// </summary>
public interface IPlugin
{
    /// <summary>
    /// 插件信息
    /// </summary>
    PluginInfo PluginInfo { get; }

    /// <summary>
    /// 初始化插件
    /// </summary>
    /// <param name="serviceProvider">服务提供者</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>初始化任务</returns>
    Task InitializeAsync(IServiceProvider serviceProvider, CancellationToken cancellationToken = default);

    /// <summary>
    /// 启动插件
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>启动任务</returns>
    Task StartAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 停止插件
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>停止任务</returns>
    Task StopAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 释放插件资源
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>释放任务</returns>
    Task DisposeAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 创建节点执行器
    /// </summary>
    /// <param name="nodeType">节点类型</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>节点执行器</returns>
    Task<INodeExecutor?> CreateNodeExecutorAsync(string nodeType, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取节点类型定义列表
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>节点类型定义列表</returns>
    Task<IEnumerable<NodeTypeDefinition>> GetNodeTypeDefinitionsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 配置服务
    /// </summary>
    /// <param name="services">服务集合</param>
    void ConfigureServices(IServiceCollection services);

    /// <summary>
    /// 验证插件配置
    /// </summary>
    /// <param name="configuration">插件配置</param>
    /// <returns>验证结果</returns>
    Task<PluginValidationResult> ValidateConfigurationAsync(Dictionary<string, object> configuration);

    /// <summary>
    /// 获取插件健康状态
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>健康状态</returns>
    Task<PluginHealthStatus> GetHealthStatusAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// 插件健康状态
/// </summary>
public class PluginHealthStatus
{
    /// <summary>
    /// 是否健康
    /// </summary>
    public bool IsHealthy { get; set; }

    /// <summary>
    /// 状态描述
    /// </summary>
    public string Status { get; set; } = string.Empty;

    /// <summary>
    /// 详细信息
    /// </summary>
    public string? Details { get; set; }

    /// <summary>
    /// 最后检查时间
    /// </summary>
    public DateTime LastChecked { get; set; }

    /// <summary>
    /// 额外数据
    /// </summary>
    public Dictionary<string, object> Data { get; set; } = new();
}
