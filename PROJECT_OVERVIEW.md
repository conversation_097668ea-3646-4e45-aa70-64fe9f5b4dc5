# FlowCustomV1 - 插件化工作流自动化平台

## 项目概述

FlowCustomV1 是一个基于 .NET 8 和 React 的现代化工作流自动化平台，采用完全插件化架构，支持可视化流程设计、动态节点加载和自动化执行。

## 技术栈

### 后端
- **.NET 8.0** - 现代化的 .NET 平台
- **ASP.NET Core Web API** - RESTful API 服务
- **Entity Framework Core** - ORM 数据访问
- **SQLite** - 轻量级数据库
- **插件系统** - 基于 AssemblyLoadContext 的动态加载

### 前端
- **React 18** - 现代化前端框架
- **TypeScript** - 类型安全的 JavaScript
- **Vite** - 快速的构建工具
- **ReactFlow** - 专业的流程图组件
- **Tailwind CSS** - 实用优先的 CSS 框架

## 项目结构

```
FlowCustomV1/
├── src/                           # 源代码目录
│   ├── FlowCustomV1.Core/         # 核心框架
│   ├── FlowCustomV1.SDK/          # 插件开发SDK
│   ├── FlowCustomV1.Data/         # 数据访问层
│   ├── FlowCustomV1.Engine/       # 工作流执行引擎
│   ├── FlowCustomV1.PluginHost/   # 插件宿主
│   └── FlowCustomV1.Api/          # Web API
├── plugins/                       # 插件目录
│   ├── FlowCustomV1.Plugins.Core/ # 核心插件
│   ├── FlowCustomV1.Plugins.Data/ # 数据插件
│   └── FlowCustomV1.Plugins.Http/ # HTTP插件
├── frontend/                      # 前端应用
├── tests/                         # 测试项目
├── docs/                          # 文档
├── scripts/                       # 构建脚本
└── runtime/                       # 运行时目录
```

## 核心组件

### 1. FlowCustomV1.Core - 核心框架

**主要功能：**
- 定义核心接口和模型
- 工作流定义和执行模型
- 插件定义和节点类型模型
- 异常处理和扩展方法

**关键文件：**
- `Models/WorkflowDefinition.cs` - 工作流定义模型
- `Models/WorkflowExecution.cs` - 工作流执行模型
- `Models/PluginDefinition.cs` - 插件定义模型
- `Interfaces/IWorkflowEngine.cs` - 工作流引擎接口
- `Interfaces/IPluginManager.cs` - 插件管理器接口
- `Interfaces/INodeExecutor.cs` - 节点执行器接口

### 2. FlowCustomV1.SDK - 插件开发SDK

**主要功能：**
- 提供插件开发基础类
- 节点执行器基类
- 特性标注系统
- 帮助工具类

**关键文件：**
- `BasePlugin.cs` - 插件基类
- `BaseNodeExecutor.cs` - 节点执行器基类
- `Attributes/NodeTypeAttribute.cs` - 节点类型特性
- `Helpers/NodeTypeDefinitionHelper.cs` - 节点类型定义帮助类

### 3. FlowCustomV1.Data - 数据访问层

**主要功能：**
- Entity Framework Core 数据上下文
- 仓储模式实现
- 数据模型配置

**关键文件：**
- `FlowCustomDbContext.cs` - 数据库上下文
- `Repositories/WorkflowRepository.cs` - 工作流仓储
- `Repositories/WorkflowExecutionRepository.cs` - 执行记录仓储

### 4. FlowCustomV1.Engine - 工作流执行引擎

**主要功能：**
- 工作流执行逻辑
- 节点调度和执行
- 错误处理和重试
- 执行状态管理

### 5. FlowCustomV1.PluginHost - 插件宿主

**主要功能：**
- 插件动态加载和卸载
- 插件生命周期管理
- 插件隔离和安全
- 依赖注入集成

### 6. FlowCustomV1.Api - Web API

**主要功能：**
- RESTful API 接口
- 工作流管理
- 插件管理
- 执行监控

**关键文件：**
- `Controllers/WorkflowsController.cs` - 工作流控制器
- `Controllers/ExecutionsController.cs` - 执行控制器
- `Controllers/PluginsController.cs` - 插件控制器

## 插件系统

### 核心插件包

1. **FlowCustomV1.Plugins.Core** - 核心节点
   - 开始节点
   - 结束节点
   - 条件节点
   - 循环节点
   - 变量节点

2. **FlowCustomV1.Plugins.Data** - 数据处理节点
   - 数据库查询节点
   - 数据转换节点
   - 文件操作节点
   - JSON处理节点

3. **FlowCustomV1.Plugins.Http** - HTTP节点
   - HTTP请求节点
   - API调用节点
   - Webhook节点

### 插件开发指南

1. **创建插件项目**
   ```xml
   <PackageReference Include="FlowCustomV1.SDK" Version="1.0.0" />
   ```

2. **实现插件主类**
   ```csharp
   [PluginInfo("MyPlugin", "我的插件", "1.0.0")]
   public class MyPlugin : BasePlugin
   {
       // 插件实现
   }
   ```

3. **实现节点执行器**
   ```csharp
   [NodeType("MyNode", "我的节点")]
   public class MyNodeExecutor : BaseNodeExecutor
   {
       // 节点实现
   }
   ```

## 前端应用

### 主要功能
- 可视化工作流设计器
- 拖拽式节点编辑
- 实时执行监控
- 插件管理界面
- 执行历史查看

### 技术特性
- 响应式设计
- 实时数据更新
- 组件化架构
- TypeScript 类型安全

## 数据库设计

### 主要表结构
- `WorkflowDefinitions` - 工作流定义
- `WorkflowExecutions` - 工作流执行记录
- `NodeExecutions` - 节点执行记录
- `ExecutionLogs` - 执行日志
- `PluginDefinitions` - 插件定义

## API 接口

### 工作流管理
- `GET /api/workflows` - 获取工作流列表
- `POST /api/workflows` - 创建工作流
- `PUT /api/workflows/{id}` - 更新工作流
- `DELETE /api/workflows/{id}` - 删除工作流
- `POST /api/workflows/{id}/execute` - 执行工作流

### 执行管理
- `GET /api/executions` - 获取执行记录
- `GET /api/executions/{id}` - 获取执行详情
- `POST /api/executions/{id}/cancel` - 取消执行

### 插件管理
- `GET /api/plugins` - 获取插件列表
- `POST /api/plugins/load` - 加载插件
- `POST /api/plugins/{name}/unload` - 卸载插件

## 部署说明

### 开发环境
1. 安装 .NET 8 SDK
2. 安装 Node.js 18+
3. 克隆项目并还原依赖
4. 运行数据库迁移
5. 启动后端和前端服务

### 生产环境
1. 构建 Docker 镜像
2. 配置数据库连接
3. 部署到容器平台
4. 配置反向代理

## 扩展性

### 插件扩展
- 支持第三方插件开发
- 热插拔插件加载
- 插件版本管理
- 插件依赖解析

### 功能扩展
- 自定义节点类型
- 工作流模板
- 触发器系统
- 监控和告警

## 安全性

### 插件安全
- 插件沙箱隔离
- 权限控制
- 代码签名验证

### API 安全
- 身份认证
- 授权控制
- 输入验证
- 审计日志

## 性能优化

### 执行性能
- 异步执行
- 并行处理
- 资源池管理
- 缓存优化

### 数据库性能
- 索引优化
- 查询优化
- 连接池管理
- 分页查询

## 监控和日志

### 执行监控
- 实时状态监控
- 性能指标收集
- 错误统计分析

### 日志系统
- 结构化日志
- 日志级别控制
- 日志持久化
- 日志查询分析

## 未来规划

### 短期目标
- 完善插件生态
- 优化用户体验
- 增强稳定性

### 长期目标
- 分布式执行
- 云原生支持
- AI 集成
- 企业级功能
