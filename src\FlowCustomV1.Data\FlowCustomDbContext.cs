using FlowCustomV1.Core.Models;
using Microsoft.EntityFrameworkCore;
using System.Text.Json;

namespace FlowCustomV1.Data;

/// <summary>
/// FlowCustom 数据库上下文
/// </summary>
public class FlowCustomDbContext : DbContext
{
    /// <summary>
    /// 工作流定义
    /// </summary>
    public DbSet<WorkflowDefinition> WorkflowDefinitions { get; set; } = null!;

    /// <summary>
    /// 工作流执行记录
    /// </summary>
    public DbSet<WorkflowExecution> WorkflowExecutions { get; set; } = null!;

    /// <summary>
    /// 节点执行记录
    /// </summary>
    public DbSet<NodeExecution> NodeExecutions { get; set; } = null!;

    /// <summary>
    /// 执行日志
    /// </summary>
    public DbSet<ExecutionLog> ExecutionLogs { get; set; } = null!;

    /// <summary>
    /// 插件定义
    /// </summary>
    public DbSet<PluginDefinition> PluginDefinitions { get; set; } = null!;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="options">数据库上下文选项</param>
    public FlowCustomDbContext(DbContextOptions<FlowCustomDbContext> options) : base(options)
    {
    }

    /// <summary>
    /// 配置模型
    /// </summary>
    /// <param name="modelBuilder">模型构建器</param>
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        ConfigureWorkflowDefinition(modelBuilder);
        ConfigureWorkflowExecution(modelBuilder);
        ConfigureNodeExecution(modelBuilder);
        ConfigureExecutionLog(modelBuilder);
        ConfigurePluginDefinition(modelBuilder);
    }

    /// <summary>
    /// 配置工作流定义
    /// </summary>
    /// <param name="modelBuilder">模型构建器</param>
    private static void ConfigureWorkflowDefinition(ModelBuilder modelBuilder)
    {
        var entity = modelBuilder.Entity<WorkflowDefinition>();

        entity.HasKey(e => e.Id);
        entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
        entity.Property(e => e.Description).HasMaxLength(500);
        entity.Property(e => e.CreatedBy).HasMaxLength(100);
        entity.Property(e => e.CreatedAt).IsRequired();
        entity.Property(e => e.UpdatedAt).IsRequired();

        // 配置 JSON 序列化
        entity.Property(e => e.Nodes)
            .HasConversion(
                v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                v => JsonSerializer.Deserialize<List<WorkflowNode>>(v, (JsonSerializerOptions?)null) ?? new List<WorkflowNode>());

        entity.Property(e => e.Connections)
            .HasConversion(
                v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                v => JsonSerializer.Deserialize<List<WorkflowConnection>>(v, (JsonSerializerOptions?)null) ?? new List<WorkflowConnection>());

        entity.Property(e => e.Variables)
            .HasConversion(
                v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                v => JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions?)null) ?? new Dictionary<string, object>());

        entity.Property(e => e.Configuration)
            .HasConversion(
                v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                v => JsonSerializer.Deserialize<WorkflowConfiguration>(v, (JsonSerializerOptions?)null) ?? new WorkflowConfiguration());

        // 索引
        entity.HasIndex(e => e.Name).IsUnique();
        entity.HasIndex(e => e.IsEnabled);
        entity.HasIndex(e => e.CreatedAt);
    }

    /// <summary>
    /// 配置工作流执行记录
    /// </summary>
    /// <param name="modelBuilder">模型构建器</param>
    private static void ConfigureWorkflowExecution(ModelBuilder modelBuilder)
    {
        var entity = modelBuilder.Entity<WorkflowExecution>();

        entity.HasKey(e => e.Id);
        entity.Property(e => e.WorkflowDefinitionId).IsRequired();
        entity.Property(e => e.TriggeredBy).HasMaxLength(100);
        entity.Property(e => e.CreatedAt).IsRequired();

        // 配置 JSON 序列化
        entity.Property(e => e.InputData)
            .HasConversion(
                v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                v => JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions?)null) ?? new Dictionary<string, object>());

        entity.Property(e => e.OutputData)
            .HasConversion(
                v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                v => JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions?)null) ?? new Dictionary<string, object>());

        entity.Property(e => e.Context)
            .HasConversion(
                v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                v => JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions?)null) ?? new Dictionary<string, object>());

        // 关系
        entity.HasOne(e => e.WorkflowDefinition)
            .WithMany()
            .HasForeignKey(e => e.WorkflowDefinitionId)
            .OnDelete(DeleteBehavior.Cascade);

        entity.HasMany(e => e.NodeExecutions)
            .WithOne()
            .HasForeignKey(ne => ne.WorkflowExecutionId)
            .OnDelete(DeleteBehavior.Cascade);

        entity.HasMany(e => e.Logs)
            .WithOne()
            .HasForeignKey(l => l.WorkflowExecutionId)
            .OnDelete(DeleteBehavior.Cascade);

        // 索引
        entity.HasIndex(e => e.WorkflowDefinitionId);
        entity.HasIndex(e => e.Status);
        entity.HasIndex(e => e.CreatedAt);
        entity.HasIndex(e => e.StartedAt);
        entity.HasIndex(e => e.CompletedAt);
    }

    /// <summary>
    /// 配置节点执行记录
    /// </summary>
    /// <param name="modelBuilder">模型构建器</param>
    private static void ConfigureNodeExecution(ModelBuilder modelBuilder)
    {
        var entity = modelBuilder.Entity<NodeExecution>();

        entity.HasKey(e => e.Id);
        entity.Property(e => e.WorkflowExecutionId).IsRequired();
        entity.Property(e => e.NodeId).IsRequired().HasMaxLength(100);
        entity.Property(e => e.NodeName).IsRequired().HasMaxLength(100);
        entity.Property(e => e.NodeType).IsRequired().HasMaxLength(100);

        // 配置 JSON 序列化
        entity.Property(e => e.InputData)
            .HasConversion(
                v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                v => JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions?)null) ?? new Dictionary<string, object>());

        entity.Property(e => e.OutputData)
            .HasConversion(
                v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                v => JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions?)null) ?? new Dictionary<string, object>());

        // 索引
        entity.HasIndex(e => e.WorkflowExecutionId);
        entity.HasIndex(e => e.NodeId);
        entity.HasIndex(e => e.Status);
        entity.HasIndex(e => e.StartedAt);
        entity.HasIndex(e => e.CompletedAt);
    }

    /// <summary>
    /// 配置执行日志
    /// </summary>
    /// <param name="modelBuilder">模型构建器</param>
    private static void ConfigureExecutionLog(ModelBuilder modelBuilder)
    {
        var entity = modelBuilder.Entity<ExecutionLog>();

        entity.HasKey(e => e.Id);
        entity.Property(e => e.WorkflowExecutionId).IsRequired();
        entity.Property(e => e.Message).IsRequired();
        entity.Property(e => e.Source).HasMaxLength(100);
        entity.Property(e => e.Timestamp).IsRequired();

        // 索引
        entity.HasIndex(e => e.WorkflowExecutionId);
        entity.HasIndex(e => e.NodeExecutionId);
        entity.HasIndex(e => e.Level);
        entity.HasIndex(e => e.Timestamp);
    }

    /// <summary>
    /// 配置插件定义
    /// </summary>
    /// <param name="modelBuilder">模型构建器</param>
    private static void ConfigurePluginDefinition(ModelBuilder modelBuilder)
    {
        var entity = modelBuilder.Entity<PluginDefinition>();

        entity.HasKey(e => e.Id);
        entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
        entity.Property(e => e.DisplayName).IsRequired().HasMaxLength(100);
        entity.Property(e => e.Description).HasMaxLength(500);
        entity.Property(e => e.Version).IsRequired().HasMaxLength(20);
        entity.Property(e => e.Author).HasMaxLength(100);
        entity.Property(e => e.Category).HasMaxLength(50);
        entity.Property(e => e.FilePath).IsRequired();
        entity.Property(e => e.AssemblyName).IsRequired();
        entity.Property(e => e.MainTypeName).IsRequired();
        entity.Property(e => e.CreatedAt).IsRequired();
        entity.Property(e => e.UpdatedAt).IsRequired();

        // 配置 JSON 序列化
        entity.Property(e => e.Tags)
            .HasConversion(
                v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                v => JsonSerializer.Deserialize<List<string>>(v, (JsonSerializerOptions?)null) ?? new List<string>());

        entity.Property(e => e.Dependencies)
            .HasConversion(
                v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                v => JsonSerializer.Deserialize<List<PluginDependency>>(v, (JsonSerializerOptions?)null) ?? new List<PluginDependency>());

        entity.Property(e => e.NodeTypes)
            .HasConversion(
                v => JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                v => JsonSerializer.Deserialize<List<NodeTypeDefinition>>(v, (JsonSerializerOptions?)null) ?? new List<NodeTypeDefinition>());

        entity.Property(e => e.ConfigurationSchema)
            .HasConversion(
                v => v == null ? null : JsonSerializer.Serialize(v, (JsonSerializerOptions?)null),
                v => v == null ? null : JsonSerializer.Deserialize<Dictionary<string, object>>(v, (JsonSerializerOptions?)null));

        // 索引
        entity.HasIndex(e => e.Name).IsUnique();
        entity.HasIndex(e => e.IsEnabled);
        entity.HasIndex(e => e.LoadStatus);
        entity.HasIndex(e => e.Category);
        entity.HasIndex(e => e.CreatedAt);
    }
}
