namespace FlowCustomV1.Core.Exceptions;

/// <summary>
/// FlowCustom 基础异常
/// </summary>
public abstract class FlowCustomException : Exception
{
    /// <summary>
    /// 错误代码
    /// </summary>
    public string ErrorCode { get; }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="errorCode">错误代码</param>
    /// <param name="message">错误消息</param>
    protected FlowCustomException(string errorCode, string message) : base(message)
    {
        ErrorCode = errorCode;
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="errorCode">错误代码</param>
    /// <param name="message">错误消息</param>
    /// <param name="innerException">内部异常</param>
    protected FlowCustomException(string errorCode, string message, Exception innerException) : base(message, innerException)
    {
        ErrorCode = errorCode;
    }
}

/// <summary>
/// 工作流异常
/// </summary>
public class WorkflowException : FlowCustomException
{
    /// <summary>
    /// 工作流ID
    /// </summary>
    public Guid? WorkflowId { get; }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="message">错误消息</param>
    /// <param name="workflowId">工作流ID</param>
    public WorkflowException(string message, Guid? workflowId = null) 
        : base("WORKFLOW_ERROR", message)
    {
        WorkflowId = workflowId;
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="message">错误消息</param>
    /// <param name="innerException">内部异常</param>
    /// <param name="workflowId">工作流ID</param>
    public WorkflowException(string message, Exception innerException, Guid? workflowId = null) 
        : base("WORKFLOW_ERROR", message, innerException)
    {
        WorkflowId = workflowId;
    }
}

/// <summary>
/// 工作流验证异常
/// </summary>
public class WorkflowValidationException : WorkflowException
{
    /// <summary>
    /// 验证错误列表
    /// </summary>
    public List<string> ValidationErrors { get; }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="validationErrors">验证错误列表</param>
    /// <param name="workflowId">工作流ID</param>
    public WorkflowValidationException(List<string> validationErrors, Guid? workflowId = null) 
        : base($"工作流验证失败: {string.Join(", ", validationErrors)}", workflowId)
    {
        ValidationErrors = validationErrors;
    }
}

/// <summary>
/// 工作流执行异常
/// </summary>
public class WorkflowExecutionException : WorkflowException
{
    /// <summary>
    /// 执行ID
    /// </summary>
    public Guid? ExecutionId { get; }

    /// <summary>
    /// 节点ID
    /// </summary>
    public string? NodeId { get; }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="message">错误消息</param>
    /// <param name="executionId">执行ID</param>
    /// <param name="nodeId">节点ID</param>
    /// <param name="workflowId">工作流ID</param>
    public WorkflowExecutionException(string message, Guid? executionId = null, string? nodeId = null, Guid? workflowId = null) 
        : base(message, workflowId)
    {
        ExecutionId = executionId;
        NodeId = nodeId;
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="message">错误消息</param>
    /// <param name="innerException">内部异常</param>
    /// <param name="executionId">执行ID</param>
    /// <param name="nodeId">节点ID</param>
    /// <param name="workflowId">工作流ID</param>
    public WorkflowExecutionException(string message, Exception innerException, Guid? executionId = null, string? nodeId = null, Guid? workflowId = null) 
        : base(message, innerException, workflowId)
    {
        ExecutionId = executionId;
        NodeId = nodeId;
    }
}

/// <summary>
/// 插件异常
/// </summary>
public class PluginException : FlowCustomException
{
    /// <summary>
    /// 插件名称
    /// </summary>
    public string? PluginName { get; }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="message">错误消息</param>
    /// <param name="pluginName">插件名称</param>
    public PluginException(string message, string? pluginName = null) 
        : base("PLUGIN_ERROR", message)
    {
        PluginName = pluginName;
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="message">错误消息</param>
    /// <param name="innerException">内部异常</param>
    /// <param name="pluginName">插件名称</param>
    public PluginException(string message, Exception innerException, string? pluginName = null) 
        : base("PLUGIN_ERROR", message, innerException)
    {
        PluginName = pluginName;
    }
}

/// <summary>
/// 插件加载异常
/// </summary>
public class PluginLoadException : PluginException
{
    /// <summary>
    /// 插件路径
    /// </summary>
    public string? PluginPath { get; }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="message">错误消息</param>
    /// <param name="pluginPath">插件路径</param>
    /// <param name="pluginName">插件名称</param>
    public PluginLoadException(string message, string? pluginPath = null, string? pluginName = null) 
        : base(message, pluginName)
    {
        PluginPath = pluginPath;
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="message">错误消息</param>
    /// <param name="innerException">内部异常</param>
    /// <param name="pluginPath">插件路径</param>
    /// <param name="pluginName">插件名称</param>
    public PluginLoadException(string message, Exception innerException, string? pluginPath = null, string? pluginName = null) 
        : base(message, innerException, pluginName)
    {
        PluginPath = pluginPath;
    }
}

/// <summary>
/// 节点执行异常
/// </summary>
public class NodeExecutionException : FlowCustomException
{
    /// <summary>
    /// 节点ID
    /// </summary>
    public string? NodeId { get; }

    /// <summary>
    /// 节点类型
    /// </summary>
    public string? NodeType { get; }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="message">错误消息</param>
    /// <param name="nodeId">节点ID</param>
    /// <param name="nodeType">节点类型</param>
    public NodeExecutionException(string message, string? nodeId = null, string? nodeType = null) 
        : base("NODE_EXECUTION_ERROR", message)
    {
        NodeId = nodeId;
        NodeType = nodeType;
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="message">错误消息</param>
    /// <param name="innerException">内部异常</param>
    /// <param name="nodeId">节点ID</param>
    /// <param name="nodeType">节点类型</param>
    public NodeExecutionException(string message, Exception innerException, string? nodeId = null, string? nodeType = null) 
        : base("NODE_EXECUTION_ERROR", message, innerException)
    {
        NodeId = nodeId;
        NodeType = nodeType;
    }
}

/// <summary>
/// 配置异常
/// </summary>
public class ConfigurationException : FlowCustomException
{
    /// <summary>
    /// 配置键
    /// </summary>
    public string? ConfigurationKey { get; }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="message">错误消息</param>
    /// <param name="configurationKey">配置键</param>
    public ConfigurationException(string message, string? configurationKey = null) 
        : base("CONFIGURATION_ERROR", message)
    {
        ConfigurationKey = configurationKey;
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="message">错误消息</param>
    /// <param name="innerException">内部异常</param>
    /// <param name="configurationKey">配置键</param>
    public ConfigurationException(string message, Exception innerException, string? configurationKey = null) 
        : base("CONFIGURATION_ERROR", message, innerException)
    {
        ConfigurationKey = configurationKey;
    }
}
