using FlowCustomV1.Core.Models;

namespace FlowCustomV1.Core.Interfaces;

/// <summary>
/// 节点执行器接口
/// </summary>
public interface INodeExecutor
{
    /// <summary>
    /// 节点类型
    /// </summary>
    string NodeType { get; }

    /// <summary>
    /// 执行节点
    /// </summary>
    /// <param name="context">执行上下文</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>执行结果</returns>
    Task<NodeExecutionResult> ExecuteAsync(NodeExecutionContext context, CancellationToken cancellationToken = default);

    /// <summary>
    /// 验证节点配置
    /// </summary>
    /// <param name="configuration">节点配置</param>
    /// <returns>验证结果</returns>
    Task<NodeValidationResult> ValidateAsync(Dictionary<string, object> configuration);

    /// <summary>
    /// 获取节点类型定义
    /// </summary>
    /// <returns>节点类型定义</returns>
    NodeTypeDefinition GetNodeTypeDefinition();

    /// <summary>
    /// 初始化节点执行器
    /// </summary>
    /// <param name="configuration">节点配置</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>初始化任务</returns>
    Task InitializeAsync(Dictionary<string, object> configuration, CancellationToken cancellationToken = default);

    /// <summary>
    /// 清理资源
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>清理任务</returns>
    Task CleanupAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// 节点执行上下文
/// </summary>
public class NodeExecutionContext
{
    /// <summary>
    /// 节点执行ID
    /// </summary>
    public Guid NodeExecutionId { get; set; }

    /// <summary>
    /// 工作流执行ID
    /// </summary>
    public Guid WorkflowExecutionId { get; set; }

    /// <summary>
    /// 节点定义
    /// </summary>
    public WorkflowNode Node { get; set; } = null!;

    /// <summary>
    /// 输入数据
    /// </summary>
    public Dictionary<string, object> InputData { get; set; } = new();

    /// <summary>
    /// 工作流变量
    /// </summary>
    public Dictionary<string, object> Variables { get; set; } = new();

    /// <summary>
    /// 执行上下文数据
    /// </summary>
    public Dictionary<string, object> Context { get; set; } = new();

    /// <summary>
    /// 日志记录器
    /// </summary>
    public IExecutionLogger Logger { get; set; } = null!;

    /// <summary>
    /// 服务提供者
    /// </summary>
    public IServiceProvider ServiceProvider { get; set; } = null!;

    /// <summary>
    /// 取消令牌
    /// </summary>
    public CancellationToken CancellationToken { get; set; }
}

/// <summary>
/// 节点执行结果
/// </summary>
public class NodeExecutionResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 输出数据
    /// </summary>
    public Dictionary<string, object> OutputData { get; set; } = new();

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 错误详情
    /// </summary>
    public string? ErrorDetails { get; set; }

    /// <summary>
    /// 异常
    /// </summary>
    public Exception? Exception { get; set; }

    /// <summary>
    /// 是否应该重试
    /// </summary>
    public bool ShouldRetry { get; set; } = false;

    /// <summary>
    /// 重试延迟（毫秒）
    /// </summary>
    public int RetryDelayMs { get; set; } = 1000;

    /// <summary>
    /// 执行状态
    /// </summary>
    public NodeExecutionStatus Status { get; set; } = NodeExecutionStatus.Completed;

    /// <summary>
    /// 创建成功结果
    /// </summary>
    /// <param name="outputData">输出数据</param>
    /// <returns>成功结果</returns>
    public static NodeExecutionResult Success(Dictionary<string, object>? outputData = null)
    {
        return new NodeExecutionResult
        {
            IsSuccess = true,
            OutputData = outputData ?? new Dictionary<string, object>(),
            Status = NodeExecutionStatus.Completed
        };
    }

    /// <summary>
    /// 创建失败结果
    /// </summary>
    /// <param name="errorMessage">错误信息</param>
    /// <param name="exception">异常</param>
    /// <param name="shouldRetry">是否应该重试</param>
    /// <returns>失败结果</returns>
    public static NodeExecutionResult Failure(string errorMessage, Exception? exception = null, bool shouldRetry = false)
    {
        return new NodeExecutionResult
        {
            IsSuccess = false,
            ErrorMessage = errorMessage,
            Exception = exception,
            ErrorDetails = exception?.ToString(),
            ShouldRetry = shouldRetry,
            Status = NodeExecutionStatus.Failed
        };
    }

    /// <summary>
    /// 创建跳过结果
    /// </summary>
    /// <param name="reason">跳过原因</param>
    /// <returns>跳过结果</returns>
    public static NodeExecutionResult Skipped(string reason)
    {
        return new NodeExecutionResult
        {
            IsSuccess = true,
            ErrorMessage = reason,
            Status = NodeExecutionStatus.Skipped
        };
    }
}

/// <summary>
/// 节点验证结果
/// </summary>
public class NodeValidationResult
{
    /// <summary>
    /// 是否有效
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// 错误列表
    /// </summary>
    public List<string> Errors { get; set; } = new();

    /// <summary>
    /// 警告列表
    /// </summary>
    public List<string> Warnings { get; set; } = new();

    /// <summary>
    /// 创建有效结果
    /// </summary>
    /// <returns>有效结果</returns>
    public static NodeValidationResult Valid()
    {
        return new NodeValidationResult { IsValid = true };
    }

    /// <summary>
    /// 创建无效结果
    /// </summary>
    /// <param name="errors">错误列表</param>
    /// <returns>无效结果</returns>
    public static NodeValidationResult Invalid(params string[] errors)
    {
        return new NodeValidationResult
        {
            IsValid = false,
            Errors = errors.ToList()
        };
    }

    /// <summary>
    /// 创建带警告的有效结果
    /// </summary>
    /// <param name="warnings">警告列表</param>
    /// <returns>带警告的有效结果</returns>
    public static NodeValidationResult ValidWithWarnings(params string[] warnings)
    {
        return new NodeValidationResult
        {
            IsValid = true,
            Warnings = warnings.ToList()
        };
    }
}

/// <summary>
/// 执行日志记录器接口
/// </summary>
public interface IExecutionLogger
{
    /// <summary>
    /// 记录跟踪日志
    /// </summary>
    /// <param name="message">消息</param>
    /// <param name="args">参数</param>
    void LogTrace(string message, params object[] args);

    /// <summary>
    /// 记录调试日志
    /// </summary>
    /// <param name="message">消息</param>
    /// <param name="args">参数</param>
    void LogDebug(string message, params object[] args);

    /// <summary>
    /// 记录信息日志
    /// </summary>
    /// <param name="message">消息</param>
    /// <param name="args">参数</param>
    void LogInformation(string message, params object[] args);

    /// <summary>
    /// 记录警告日志
    /// </summary>
    /// <param name="message">消息</param>
    /// <param name="args">参数</param>
    void LogWarning(string message, params object[] args);

    /// <summary>
    /// 记录错误日志
    /// </summary>
    /// <param name="message">消息</param>
    /// <param name="exception">异常</param>
    /// <param name="args">参数</param>
    void LogError(string message, Exception? exception = null, params object[] args);

    /// <summary>
    /// 记录严重错误日志
    /// </summary>
    /// <param name="message">消息</param>
    /// <param name="exception">异常</param>
    /// <param name="args">参数</param>
    void LogCritical(string message, Exception? exception = null, params object[] args);
}
