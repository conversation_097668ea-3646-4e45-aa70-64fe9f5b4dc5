namespace FlowCustomV1.SDK.Attributes;

/// <summary>
/// 节点类型特性
/// </summary>
[AttributeUsage(AttributeTargets.Class, AllowMultiple = false)]
public class NodeTypeAttribute : Attribute
{
    /// <summary>
    /// 节点类型名称
    /// </summary>
    public string TypeName { get; }

    /// <summary>
    /// 节点显示名称
    /// </summary>
    public string DisplayName { get; }

    /// <summary>
    /// 节点描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 节点图标
    /// </summary>
    public string? Icon { get; set; }

    /// <summary>
    /// 节点类别
    /// </summary>
    public string? Category { get; set; }

    /// <summary>
    /// 节点标签
    /// </summary>
    public string[]? Tags { get; set; }

    /// <summary>
    /// 是否支持动态端点
    /// </summary>
    public bool SupportsDynamicEndpoints { get; set; } = false;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="typeName">节点类型名称</param>
    /// <param name="displayName">节点显示名称</param>
    public NodeTypeAttribute(string typeName, string displayName)
    {
        TypeName = typeName;
        DisplayName = displayName;
    }
}

/// <summary>
/// 输入端点特性
/// </summary>
[AttributeUsage(AttributeTargets.Property, AllowMultiple = false)]
public class InputEndpointAttribute : Attribute
{
    /// <summary>
    /// 端点名称
    /// </summary>
    public string Name { get; }

    /// <summary>
    /// 端点显示名称
    /// </summary>
    public string DisplayName { get; }

    /// <summary>
    /// 端点描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 端点类型
    /// </summary>
    public string? Type { get; set; }

    /// <summary>
    /// 是否必需
    /// </summary>
    public bool IsRequired { get; set; } = false;

    /// <summary>
    /// 默认值
    /// </summary>
    public object? DefaultValue { get; set; }

    /// <summary>
    /// 是否支持多连接
    /// </summary>
    public bool AllowMultipleConnections { get; set; } = false;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="name">端点名称</param>
    /// <param name="displayName">端点显示名称</param>
    public InputEndpointAttribute(string name, string displayName)
    {
        Name = name;
        DisplayName = displayName;
    }
}

/// <summary>
/// 输出端点特性
/// </summary>
[AttributeUsage(AttributeTargets.Property, AllowMultiple = false)]
public class OutputEndpointAttribute : Attribute
{
    /// <summary>
    /// 端点名称
    /// </summary>
    public string Name { get; }

    /// <summary>
    /// 端点显示名称
    /// </summary>
    public string DisplayName { get; }

    /// <summary>
    /// 端点描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 端点类型
    /// </summary>
    public string? Type { get; set; }

    /// <summary>
    /// 是否支持多连接
    /// </summary>
    public bool AllowMultipleConnections { get; set; } = true;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="name">端点名称</param>
    /// <param name="displayName">端点显示名称</param>
    public OutputEndpointAttribute(string name, string displayName)
    {
        Name = name;
        DisplayName = displayName;
    }
}

/// <summary>
/// 配置属性特性
/// </summary>
[AttributeUsage(AttributeTargets.Property, AllowMultiple = false)]
public class ConfigurationPropertyAttribute : Attribute
{
    /// <summary>
    /// 属性名称
    /// </summary>
    public string Name { get; }

    /// <summary>
    /// 属性显示名称
    /// </summary>
    public string DisplayName { get; }

    /// <summary>
    /// 属性描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 属性类型
    /// </summary>
    public string? Type { get; set; }

    /// <summary>
    /// 是否必需
    /// </summary>
    public bool IsRequired { get; set; } = false;

    /// <summary>
    /// 默认值
    /// </summary>
    public object? DefaultValue { get; set; }

    /// <summary>
    /// 验证规则
    /// </summary>
    public string? ValidationRules { get; set; }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="name">属性名称</param>
    /// <param name="displayName">属性显示名称</param>
    public ConfigurationPropertyAttribute(string name, string displayName)
    {
        Name = name;
        DisplayName = displayName;
    }
}

/// <summary>
/// 插件信息特性
/// </summary>
[AttributeUsage(AttributeTargets.Class, AllowMultiple = false)]
public class PluginInfoAttribute : Attribute
{
    /// <summary>
    /// 插件名称
    /// </summary>
    public string Name { get; }

    /// <summary>
    /// 插件显示名称
    /// </summary>
    public string DisplayName { get; }

    /// <summary>
    /// 插件版本
    /// </summary>
    public string Version { get; }

    /// <summary>
    /// 插件描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 插件作者
    /// </summary>
    public string? Author { get; set; }

    /// <summary>
    /// 插件图标
    /// </summary>
    public string? Icon { get; set; }

    /// <summary>
    /// 插件类别
    /// </summary>
    public string? Category { get; set; }

    /// <summary>
    /// 插件标签
    /// </summary>
    public string[]? Tags { get; set; }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="name">插件名称</param>
    /// <param name="displayName">插件显示名称</param>
    /// <param name="version">插件版本</param>
    public PluginInfoAttribute(string name, string displayName, string version)
    {
        Name = name;
        DisplayName = displayName;
        Version = version;
    }
}
