using FlowCustomV1.Core.Models;
using FlowCustomV1.SDK.Attributes;
using System.Reflection;

namespace FlowCustomV1.SDK.Helpers;

/// <summary>
/// 节点类型定义帮助类
/// </summary>
public static class NodeTypeDefinitionHelper
{
    /// <summary>
    /// 从类型创建节点类型定义
    /// </summary>
    /// <param name="executorType">执行器类型</param>
    /// <returns>节点类型定义</returns>
    public static NodeTypeDefinition CreateFromType(Type executorType)
    {
        var nodeTypeAttr = executorType.GetCustomAttribute<NodeTypeAttribute>();
        if (nodeTypeAttr == null)
        {
            throw new InvalidOperationException($"类型 {executorType.Name} 缺少 NodeTypeAttribute 特性");
        }

        var definition = new NodeTypeDefinition
        {
            TypeName = nodeTypeAttr.TypeName,
            DisplayName = nodeTypeAttr.DisplayName,
            Description = nodeTypeAttr.Description,
            Icon = nodeTypeAttr.Icon,
            Category = nodeTypeAttr.Category,
            Tags = nodeTypeAttr.Tags?.ToList() ?? new List<string>(),
            SupportsDynamicEndpoints = nodeTypeAttr.SupportsDynamicEndpoints,
            ExecutorTypeName = executorType.FullName!,
            InputEndpoints = GetInputEndpoints(executorType),
            OutputEndpoints = GetOutputEndpoints(executorType),
            ConfigurationSchema = GetConfigurationSchema(executorType)
        };

        return definition;
    }

    /// <summary>
    /// 获取输入端点定义
    /// </summary>
    /// <param name="executorType">执行器类型</param>
    /// <returns>输入端点定义列表</returns>
    private static List<EndpointDefinition> GetInputEndpoints(Type executorType)
    {
        var endpoints = new List<EndpointDefinition>();

        var properties = executorType.GetProperties(BindingFlags.Public | BindingFlags.Instance);
        foreach (var property in properties)
        {
            var inputAttr = property.GetCustomAttribute<InputEndpointAttribute>();
            if (inputAttr != null)
            {
                endpoints.Add(new EndpointDefinition
                {
                    Name = inputAttr.Name,
                    DisplayName = inputAttr.DisplayName,
                    Description = inputAttr.Description,
                    Type = inputAttr.Type ?? GetTypeString(property.PropertyType),
                    IsRequired = inputAttr.IsRequired,
                    DefaultValue = inputAttr.DefaultValue,
                    AllowMultipleConnections = inputAttr.AllowMultipleConnections
                });
            }
        }

        return endpoints;
    }

    /// <summary>
    /// 获取输出端点定义
    /// </summary>
    /// <param name="executorType">执行器类型</param>
    /// <returns>输出端点定义列表</returns>
    private static List<EndpointDefinition> GetOutputEndpoints(Type executorType)
    {
        var endpoints = new List<EndpointDefinition>();

        var properties = executorType.GetProperties(BindingFlags.Public | BindingFlags.Instance);
        foreach (var property in properties)
        {
            var outputAttr = property.GetCustomAttribute<OutputEndpointAttribute>();
            if (outputAttr != null)
            {
                endpoints.Add(new EndpointDefinition
                {
                    Name = outputAttr.Name,
                    DisplayName = outputAttr.DisplayName,
                    Description = outputAttr.Description,
                    Type = outputAttr.Type ?? GetTypeString(property.PropertyType),
                    IsRequired = false, // 输出端点通常不是必需的
                    AllowMultipleConnections = outputAttr.AllowMultipleConnections
                });
            }
        }

        return endpoints;
    }

    /// <summary>
    /// 获取配置架构
    /// </summary>
    /// <param name="executorType">执行器类型</param>
    /// <returns>配置架构</returns>
    private static Dictionary<string, object>? GetConfigurationSchema(Type executorType)
    {
        var schema = new Dictionary<string, object>();

        var properties = executorType.GetProperties(BindingFlags.Public | BindingFlags.Instance);
        foreach (var property in properties)
        {
            var configAttr = property.GetCustomAttribute<ConfigurationPropertyAttribute>();
            if (configAttr != null)
            {
                var propertySchema = new Dictionary<string, object>
                {
                    ["name"] = configAttr.Name,
                    ["displayName"] = configAttr.DisplayName,
                    ["type"] = configAttr.Type ?? GetTypeString(property.PropertyType),
                    ["required"] = configAttr.IsRequired
                };

                if (configAttr.Description != null)
                    propertySchema["description"] = configAttr.Description;

                if (configAttr.DefaultValue != null)
                    propertySchema["defaultValue"] = configAttr.DefaultValue;

                if (configAttr.ValidationRules != null)
                    propertySchema["validationRules"] = configAttr.ValidationRules;

                schema[configAttr.Name] = propertySchema;
            }
        }

        return schema.Count > 0 ? schema : null;
    }

    /// <summary>
    /// 获取类型字符串
    /// </summary>
    /// <param name="type">类型</param>
    /// <returns>类型字符串</returns>
    private static string GetTypeString(Type type)
    {
        if (type == typeof(string))
            return "string";
        if (type == typeof(int) || type == typeof(int?))
            return "integer";
        if (type == typeof(long) || type == typeof(long?))
            return "long";
        if (type == typeof(double) || type == typeof(double?) || type == typeof(float) || type == typeof(float?))
            return "number";
        if (type == typeof(bool) || type == typeof(bool?))
            return "boolean";
        if (type == typeof(DateTime) || type == typeof(DateTime?))
            return "datetime";
        if (type == typeof(Guid) || type == typeof(Guid?))
            return "guid";
        if (type.IsArray || (type.IsGenericType && type.GetGenericTypeDefinition() == typeof(List<>)))
            return "array";
        if (type.IsClass && type != typeof(string))
            return "object";

        return "any";
    }

    /// <summary>
    /// 验证节点类型定义
    /// </summary>
    /// <param name="definition">节点类型定义</param>
    /// <returns>验证结果</returns>
    public static (bool IsValid, List<string> Errors) ValidateDefinition(NodeTypeDefinition definition)
    {
        var errors = new List<string>();

        if (string.IsNullOrWhiteSpace(definition.TypeName))
            errors.Add("节点类型名称不能为空");

        if (string.IsNullOrWhiteSpace(definition.DisplayName))
            errors.Add("节点显示名称不能为空");

        if (string.IsNullOrWhiteSpace(definition.ExecutorTypeName))
            errors.Add("执行器类型名称不能为空");

        // 验证端点名称唯一性
        var inputNames = definition.InputEndpoints.Select(e => e.Name).ToList();
        var duplicateInputs = inputNames.GroupBy(n => n).Where(g => g.Count() > 1).Select(g => g.Key);
        foreach (var duplicate in duplicateInputs)
        {
            errors.Add($"输入端点名称重复: {duplicate}");
        }

        var outputNames = definition.OutputEndpoints.Select(e => e.Name).ToList();
        var duplicateOutputs = outputNames.GroupBy(n => n).Where(g => g.Count() > 1).Select(g => g.Key);
        foreach (var duplicate in duplicateOutputs)
        {
            errors.Add($"输出端点名称重复: {duplicate}");
        }

        // 验证端点名称不能与配置属性名称冲突
        if (definition.ConfigurationSchema != null)
        {
            var configNames = definition.ConfigurationSchema.Keys.ToList();
            var conflictNames = inputNames.Concat(outputNames).Intersect(configNames);
            foreach (var conflict in conflictNames)
            {
                errors.Add($"端点名称与配置属性名称冲突: {conflict}");
            }
        }

        return (errors.Count == 0, errors);
    }

    /// <summary>
    /// 创建默认端点定义
    /// </summary>
    /// <param name="name">端点名称</param>
    /// <param name="displayName">端点显示名称</param>
    /// <param name="type">端点类型</param>
    /// <param name="isInput">是否为输入端点</param>
    /// <param name="isRequired">是否必需</param>
    /// <returns>端点定义</returns>
    public static EndpointDefinition CreateDefaultEndpoint(string name, string displayName, string type = "any", bool isInput = true, bool isRequired = false)
    {
        return new EndpointDefinition
        {
            Name = name,
            DisplayName = displayName,
            Type = type,
            IsRequired = isRequired,
            AllowMultipleConnections = !isInput // 输出端点默认允许多连接，输入端点默认不允许
        };
    }
}
