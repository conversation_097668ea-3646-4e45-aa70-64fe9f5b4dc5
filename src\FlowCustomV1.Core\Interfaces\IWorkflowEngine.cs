using FlowCustomV1.Core.Models;

namespace FlowCustomV1.Core.Interfaces;

/// <summary>
/// 工作流引擎接口
/// </summary>
public interface IWorkflowEngine
{
    /// <summary>
    /// 执行工作流
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="inputData">输入数据</param>
    /// <param name="triggeredBy">触发者</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>工作流执行记录</returns>
    Task<WorkflowExecution> ExecuteAsync(
        Guid workflowId,
        Dictionary<string, object>? inputData = null,
        string? triggeredBy = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 执行工作流（异步）
    /// </summary>
    /// <param name="workflowId">工作流ID</param>
    /// <param name="inputData">输入数据</param>
    /// <param name="triggeredBy">触发者</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>工作流执行记录</returns>
    Task<WorkflowExecution> ExecuteAsync(
        WorkflowDefinition workflow,
        Dictionary<string, object>? inputData = null,
        string? triggeredBy = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 取消工作流执行
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否取消成功</returns>
    Task<bool> CancelAsync(Guid executionId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 暂停工作流执行
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否暂停成功</returns>
    Task<bool> PauseAsync(Guid executionId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 恢复工作流执行
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否恢复成功</returns>
    Task<bool> ResumeAsync(Guid executionId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取工作流执行状态
    /// </summary>
    /// <param name="executionId">执行ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>工作流执行记录</returns>
    Task<WorkflowExecution?> GetExecutionStatusAsync(Guid executionId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 验证工作流定义
    /// </summary>
    /// <param name="workflow">工作流定义</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证结果</returns>
    Task<WorkflowValidationResult> ValidateAsync(WorkflowDefinition workflow, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取正在运行的工作流执行列表
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>正在运行的工作流执行列表</returns>
    Task<IEnumerable<WorkflowExecution>> GetRunningExecutionsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 工作流执行状态变更事件
    /// </summary>
    event EventHandler<WorkflowExecutionStatusChangedEventArgs>? ExecutionStatusChanged;

    /// <summary>
    /// 节点执行状态变更事件
    /// </summary>
    event EventHandler<NodeExecutionStatusChangedEventArgs>? NodeExecutionStatusChanged;
}

/// <summary>
/// 工作流验证结果
/// </summary>
public class WorkflowValidationResult
{
    /// <summary>
    /// 是否有效
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// 错误列表
    /// </summary>
    public List<ValidationError> Errors { get; set; } = new();

    /// <summary>
    /// 警告列表
    /// </summary>
    public List<ValidationWarning> Warnings { get; set; } = new();
}

/// <summary>
/// 验证错误
/// </summary>
public class ValidationError
{
    /// <summary>
    /// 错误代码
    /// </summary>
    public string Code { get; set; } = string.Empty;

    /// <summary>
    /// 错误消息
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// 错误位置
    /// </summary>
    public string? Location { get; set; }

    /// <summary>
    /// 相关节点ID
    /// </summary>
    public string? NodeId { get; set; }
}

/// <summary>
/// 验证警告
/// </summary>
public class ValidationWarning
{
    /// <summary>
    /// 警告代码
    /// </summary>
    public string Code { get; set; } = string.Empty;

    /// <summary>
    /// 警告消息
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// 警告位置
    /// </summary>
    public string? Location { get; set; }

    /// <summary>
    /// 相关节点ID
    /// </summary>
    public string? NodeId { get; set; }
}

/// <summary>
/// 工作流执行状态变更事件参数
/// </summary>
public class WorkflowExecutionStatusChangedEventArgs : EventArgs
{
    /// <summary>
    /// 执行ID
    /// </summary>
    public Guid ExecutionId { get; set; }

    /// <summary>
    /// 工作流ID
    /// </summary>
    public Guid WorkflowId { get; set; }

    /// <summary>
    /// 旧状态
    /// </summary>
    public WorkflowExecutionStatus OldStatus { get; set; }

    /// <summary>
    /// 新状态
    /// </summary>
    public WorkflowExecutionStatus NewStatus { get; set; }

    /// <summary>
    /// 时间戳
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// 节点执行状态变更事件参数
/// </summary>
public class NodeExecutionStatusChangedEventArgs : EventArgs
{
    /// <summary>
    /// 节点执行ID
    /// </summary>
    public Guid NodeExecutionId { get; set; }

    /// <summary>
    /// 工作流执行ID
    /// </summary>
    public Guid WorkflowExecutionId { get; set; }

    /// <summary>
    /// 节点ID
    /// </summary>
    public string NodeId { get; set; } = string.Empty;

    /// <summary>
    /// 旧状态
    /// </summary>
    public NodeExecutionStatus OldStatus { get; set; }

    /// <summary>
    /// 新状态
    /// </summary>
    public NodeExecutionStatus NewStatus { get; set; }

    /// <summary>
    /// 时间戳
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }
}
