version: '3.8'

services:
  # 后端 API 服务
  api:
    build:
      context: .
      dockerfile: src/FlowCustomV1.Api/Dockerfile
    ports:
      - "7001:80"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ConnectionStrings__DefaultConnection=Data Source=/app/data/flowcustom.db
    volumes:
      - ./data:/app/data
      - ./plugins:/app/plugins
    depends_on:
      - db
    networks:
      - flowcustom-network

  # 前端应用
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:80"
    environment:
      - REACT_APP_API_URL=http://localhost:7001
    depends_on:
      - api
    networks:
      - flowcustom-network

  # SQLite 数据库（使用文件存储）
  db:
    image: alpine:latest
    command: tail -f /dev/null
    volumes:
      - ./data:/data
    networks:
      - flowcustom-network

  # Nginx 反向代理（可选）
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - api
      - frontend
    networks:
      - flowcustom-network

volumes:
  data:
  plugins:

networks:
  flowcustom-network:
    driver: bridge
