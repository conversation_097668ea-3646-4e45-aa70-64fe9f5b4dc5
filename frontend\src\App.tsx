import React, { useState } from 'react';
import { Layout, <PERSON>u, Card, Button, Space, message } from 'antd';
import {
  AppstoreOutlined,
  SettingOutlined,
  PlayCircleOutlined,
  HistoryOutlined
} from '@ant-design/icons';
import { WorkflowDesigner } from './components/WorkflowDesigner';
import 'antd/dist/reset.css';
import './App.css';

const { Header, Sider, Content } = Layout;

function App() {
  const [selectedMenu, setSelectedMenu] = useState('designer');
  const [currentWorkflowId, setCurrentWorkflowId] = useState<string>();

  const handleSaveWorkflow = async (workflow: any) => {
    try {
      // 这里应该调用 API 保存工作流
      console.log('保存工作流:', workflow);

      // 模拟 API 调用
      const response = await fetch('/api/workflows', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(workflow),
      });

      if (response.ok) {
        const savedWorkflow = await response.json();
        setCurrentWorkflowId(savedWorkflow.id);
        message.success('工作流保存成功');
      } else {
        message.error('工作流保存失败');
      }
    } catch (error) {
      console.error('保存工作流失败:', error);
      message.error('工作流保存失败');
    }
  };

  const handleExecuteWorkflow = async (workflowId: string) => {
    try {
      // 这里应该调用 API 执行工作流
      console.log('执行工作流:', workflowId);

      // 模拟 API 调用
      const response = await fetch(`/api/workflows/${workflowId}/execute`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({}),
      });

      if (response.ok) {
        const execution = await response.json();
        message.success(`工作流执行已启动，执行ID: ${execution.id}`);
      } else {
        message.error('工作流执行失败');
      }
    } catch (error) {
      console.error('执行工作流失败:', error);
      message.error('工作流执行失败');
    }
  };

  const menuItems = [
    {
      key: 'designer',
      icon: <AppstoreOutlined />,
      label: '工作流设计器',
    },
    {
      key: 'executions',
      icon: <HistoryOutlined />,
      label: '执行历史',
    },
    {
      key: 'plugins',
      icon: <SettingOutlined />,
      label: '插件管理',
    },
  ];

  const renderContent = () => {
    switch (selectedMenu) {
      case 'designer':
        return (
          <WorkflowDesigner
            workflowId={currentWorkflowId}
            onSave={handleSaveWorkflow}
            onExecute={handleExecuteWorkflow}
          />
        );
      case 'executions':
        return (
          <Card title="执行历史">
            <p>这里将显示工作流执行历史记录</p>
          </Card>
        );
      case 'plugins':
        return (
          <Card title="插件管理">
            <p>这里将显示已安装的插件列表</p>
          </Card>
        );
      default:
        return null;
    }
  };

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Header style={{
        display: 'flex',
        alignItems: 'center',
        background: '#001529',
        color: 'white',
        padding: '0 24px'
      }}>
        <div style={{
          color: 'white',
          fontSize: '20px',
          fontWeight: 'bold',
          marginRight: '24px'
        }}>
          FlowCustomV1
        </div>
        <div style={{ color: '#8c8c8c' }}>
          插件化工作流自动化平台
        </div>
      </Header>

      <Layout>
        <Sider width={200} style={{ background: '#fff' }}>
          <Menu
            mode="inline"
            selectedKeys={[selectedMenu]}
            style={{ height: '100%', borderRight: 0 }}
            items={menuItems}
            onSelect={({ key }) => setSelectedMenu(key)}
          />
        </Sider>

        <Layout style={{ padding: '24px' }}>
          <Content
            style={{
              background: '#fff',
              padding: 24,
              margin: 0,
              minHeight: 280,
            }}
          >
            {renderContent()}
          </Content>
        </Layout>
      </Layout>
    </Layout>
  );
}

export default App;
