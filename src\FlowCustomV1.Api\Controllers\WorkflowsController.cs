using FlowCustomV1.Core.Interfaces;
using FlowCustomV1.Core.Models;
using Microsoft.AspNetCore.Mvc;

namespace FlowCustomV1.Api.Controllers;

/// <summary>
/// 工作流管理控制器
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
public class WorkflowsController : ControllerBase
{
    private readonly IWorkflowRepository _workflowRepository;
    private readonly IWorkflowEngine _workflowEngine;
    private readonly ILogger<WorkflowsController> _logger;

    /// <summary>
    /// 构造函数
    /// </summary>
    public WorkflowsController(
        IWorkflowRepository workflowRepository,
        IWorkflowEngine workflowEngine,
        ILogger<WorkflowsController> logger)
    {
        _workflowRepository = workflowRepository;
        _workflowEngine = workflowEngine;
        _logger = logger;
    }

    /// <summary>
    /// 获取所有工作流
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<WorkflowDefinition>>> GetWorkflows(CancellationToken cancellationToken)
    {
        try
        {
            var workflows = await _workflowRepository.GetAllAsync(cancellationToken);
            return Ok(workflows);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取工作流列表失败");
            return StatusCode(500, new { message = "获取工作流列表失败", error = ex.Message });
        }
    }

    /// <summary>
    /// 根据ID获取工作流
    /// </summary>
    [HttpGet("{id:guid}")]
    public async Task<ActionResult<WorkflowDefinition>> GetWorkflow(Guid id, CancellationToken cancellationToken)
    {
        try
        {
            var workflow = await _workflowRepository.GetByIdAsync(id, cancellationToken);
            if (workflow == null)
            {
                return NotFound(new { message = "工作流不存在" });
            }

            return Ok(workflow);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取工作流失败: {WorkflowId}", id);
            return StatusCode(500, new { message = "获取工作流失败", error = ex.Message });
        }
    }

    /// <summary>
    /// 创建工作流
    /// </summary>
    [HttpPost]
    public async Task<ActionResult<WorkflowDefinition>> CreateWorkflow(
        [FromBody] WorkflowDefinition workflow,
        CancellationToken cancellationToken)
    {
        try
        {
            // 验证工作流名称是否已存在
            if (await _workflowRepository.ExistsAsync(workflow.Name, cancellationToken: cancellationToken))
            {
                return BadRequest(new { message = "工作流名称已存在" });
            }

            // 验证工作流定义
            var validationResult = await _workflowEngine.ValidateAsync(workflow, cancellationToken);
            if (!validationResult.IsValid)
            {
                return BadRequest(new { 
                    message = "工作流验证失败", 
                    errors = validationResult.Errors,
                    warnings = validationResult.Warnings
                });
            }

            var createdWorkflow = await _workflowRepository.CreateAsync(workflow, cancellationToken);
            return CreatedAtAction(nameof(GetWorkflow), new { id = createdWorkflow.Id }, createdWorkflow);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建工作流失败: {WorkflowName}", workflow.Name);
            return StatusCode(500, new { message = "创建工作流失败", error = ex.Message });
        }
    }

    /// <summary>
    /// 更新工作流
    /// </summary>
    [HttpPut("{id:guid}")]
    public async Task<ActionResult<WorkflowDefinition>> UpdateWorkflow(
        Guid id,
        [FromBody] WorkflowDefinition workflow,
        CancellationToken cancellationToken)
    {
        try
        {
            if (id != workflow.Id)
            {
                return BadRequest(new { message = "工作流ID不匹配" });
            }

            var existingWorkflow = await _workflowRepository.GetByIdAsync(id, cancellationToken);
            if (existingWorkflow == null)
            {
                return NotFound(new { message = "工作流不存在" });
            }

            // 验证工作流名称是否已存在（排除当前工作流）
            if (await _workflowRepository.ExistsAsync(workflow.Name, id, cancellationToken))
            {
                return BadRequest(new { message = "工作流名称已存在" });
            }

            // 验证工作流定义
            var validationResult = await _workflowEngine.ValidateAsync(workflow, cancellationToken);
            if (!validationResult.IsValid)
            {
                return BadRequest(new { 
                    message = "工作流验证失败", 
                    errors = validationResult.Errors,
                    warnings = validationResult.Warnings
                });
            }

            var updatedWorkflow = await _workflowRepository.UpdateAsync(workflow, cancellationToken);
            return Ok(updatedWorkflow);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新工作流失败: {WorkflowId}", id);
            return StatusCode(500, new { message = "更新工作流失败", error = ex.Message });
        }
    }

    /// <summary>
    /// 删除工作流
    /// </summary>
    [HttpDelete("{id:guid}")]
    public async Task<ActionResult> DeleteWorkflow(Guid id, CancellationToken cancellationToken)
    {
        try
        {
            var success = await _workflowRepository.DeleteAsync(id, cancellationToken);
            if (!success)
            {
                return NotFound(new { message = "工作流不存在" });
            }

            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除工作流失败: {WorkflowId}", id);
            return StatusCode(500, new { message = "删除工作流失败", error = ex.Message });
        }
    }

    /// <summary>
    /// 执行工作流
    /// </summary>
    [HttpPost("{id:guid}/execute")]
    public async Task<ActionResult<WorkflowExecution>> ExecuteWorkflow(
        Guid id,
        [FromBody] Dictionary<string, object>? inputData,
        CancellationToken cancellationToken)
    {
        try
        {
            var workflow = await _workflowRepository.GetByIdAsync(id, cancellationToken);
            if (workflow == null)
            {
                return NotFound(new { message = "工作流不存在" });
            }

            if (!workflow.IsEnabled)
            {
                return BadRequest(new { message = "工作流已禁用" });
            }

            var execution = await _workflowEngine.ExecuteAsync(
                id, 
                inputData, 
                User.Identity?.Name, 
                cancellationToken);

            return Ok(execution);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "执行工作流失败: {WorkflowId}", id);
            return StatusCode(500, new { message = "执行工作流失败", error = ex.Message });
        }
    }

    /// <summary>
    /// 验证工作流
    /// </summary>
    [HttpPost("{id:guid}/validate")]
    public async Task<ActionResult<WorkflowValidationResult>> ValidateWorkflow(
        Guid id,
        CancellationToken cancellationToken)
    {
        try
        {
            var workflow = await _workflowRepository.GetByIdAsync(id, cancellationToken);
            if (workflow == null)
            {
                return NotFound(new { message = "工作流不存在" });
            }

            var validationResult = await _workflowEngine.ValidateAsync(workflow, cancellationToken);
            return Ok(validationResult);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "验证工作流失败: {WorkflowId}", id);
            return StatusCode(500, new { message = "验证工作流失败", error = ex.Message });
        }
    }

    /// <summary>
    /// 获取启用的工作流
    /// </summary>
    [HttpGet("enabled")]
    public async Task<ActionResult<IEnumerable<WorkflowDefinition>>> GetEnabledWorkflows(CancellationToken cancellationToken)
    {
        try
        {
            var workflows = await _workflowRepository.GetEnabledAsync(cancellationToken);
            return Ok(workflows);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取启用的工作流列表失败");
            return StatusCode(500, new { message = "获取启用的工作流列表失败", error = ex.Message });
        }
    }
}
