using FlowCustomV1.Core.Interfaces;
using FlowCustomV1.Core.Models;
using Microsoft.Extensions.Logging;

namespace FlowCustomV1.SDK;

/// <summary>
/// 节点执行器基类
/// </summary>
public abstract class BaseNodeExecutor : INodeExecutor
{
    /// <summary>
    /// 日志记录器
    /// </summary>
    protected ILogger Logger { get; }

    /// <summary>
    /// 节点类型
    /// </summary>
    public abstract string NodeType { get; }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="logger">日志记录器</param>
    protected BaseNodeExecutor(ILogger logger)
    {
        Logger = logger;
    }

    /// <summary>
    /// 执行节点
    /// </summary>
    /// <param name="context">执行上下文</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>执行结果</returns>
    public async Task<NodeExecutionResult> ExecuteAsync(NodeExecutionContext context, CancellationToken cancellationToken = default)
    {
        try
        {
            Logger.LogInformation("开始执行节点 {NodeType} (ID: {NodeId})", NodeType, context.Node.Id);
            
            // 验证输入数据
            var validationResult = await ValidateInputAsync(context.InputData, context.Node.Configuration);
            if (!validationResult.IsValid)
            {
                var errorMessage = $"输入数据验证失败: {string.Join(", ", validationResult.Errors)}";
                Logger.LogError(errorMessage);
                return NodeExecutionResult.Failure(errorMessage);
            }

            // 执行前处理
            await OnBeforeExecuteAsync(context, cancellationToken);

            // 执行核心逻辑
            var result = await ExecuteCoreAsync(context, cancellationToken);

            // 执行后处理
            await OnAfterExecuteAsync(context, result, cancellationToken);

            Logger.LogInformation("节点执行完成 {NodeType} (ID: {NodeId}), 状态: {Status}", 
                NodeType, context.Node.Id, result.Status);

            return result;
        }
        catch (OperationCanceledException)
        {
            Logger.LogWarning("节点执行被取消 {NodeType} (ID: {NodeId})", NodeType, context.Node.Id);
            return NodeExecutionResult.Failure("执行被取消");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "节点执行异常 {NodeType} (ID: {NodeId})", NodeType, context.Node.Id);
            return NodeExecutionResult.Failure($"执行异常: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 执行核心逻辑（子类实现）
    /// </summary>
    /// <param name="context">执行上下文</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>执行结果</returns>
    protected abstract Task<NodeExecutionResult> ExecuteCoreAsync(NodeExecutionContext context, CancellationToken cancellationToken);

    /// <summary>
    /// 验证节点配置
    /// </summary>
    /// <param name="configuration">节点配置</param>
    /// <returns>验证结果</returns>
    public virtual Task<NodeValidationResult> ValidateAsync(Dictionary<string, object> configuration)
    {
        return Task.FromResult(NodeValidationResult.Valid());
    }

    /// <summary>
    /// 获取节点类型定义
    /// </summary>
    /// <returns>节点类型定义</returns>
    public abstract NodeTypeDefinition GetNodeTypeDefinition();

    /// <summary>
    /// 初始化节点执行器
    /// </summary>
    /// <param name="configuration">节点配置</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>初始化任务</returns>
    public virtual Task InitializeAsync(Dictionary<string, object> configuration, CancellationToken cancellationToken = default)
    {
        return Task.CompletedTask;
    }

    /// <summary>
    /// 清理资源
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>清理任务</returns>
    public virtual Task CleanupAsync(CancellationToken cancellationToken = default)
    {
        return Task.CompletedTask;
    }

    /// <summary>
    /// 验证输入数据
    /// </summary>
    /// <param name="inputData">输入数据</param>
    /// <param name="configuration">节点配置</param>
    /// <returns>验证结果</returns>
    protected virtual Task<NodeValidationResult> ValidateInputAsync(Dictionary<string, object> inputData, Dictionary<string, object> configuration)
    {
        return Task.FromResult(NodeValidationResult.Valid());
    }

    /// <summary>
    /// 执行前处理
    /// </summary>
    /// <param name="context">执行上下文</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理任务</returns>
    protected virtual Task OnBeforeExecuteAsync(NodeExecutionContext context, CancellationToken cancellationToken)
    {
        return Task.CompletedTask;
    }

    /// <summary>
    /// 执行后处理
    /// </summary>
    /// <param name="context">执行上下文</param>
    /// <param name="result">执行结果</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>处理任务</returns>
    protected virtual Task OnAfterExecuteAsync(NodeExecutionContext context, NodeExecutionResult result, CancellationToken cancellationToken)
    {
        return Task.CompletedTask;
    }

    /// <summary>
    /// 获取配置值
    /// </summary>
    /// <typeparam name="T">值类型</typeparam>
    /// <param name="configuration">配置字典</param>
    /// <param name="key">配置键</param>
    /// <param name="defaultValue">默认值</param>
    /// <returns>配置值</returns>
    protected T GetConfigurationValue<T>(Dictionary<string, object> configuration, string key, T defaultValue = default!)
    {
        if (configuration.TryGetValue(key, out var value))
        {
            try
            {
                if (value is T directValue)
                    return directValue;

                return (T)Convert.ChangeType(value, typeof(T));
            }
            catch (Exception ex)
            {
                Logger.LogWarning(ex, "配置值转换失败 {Key}: {Value} -> {TargetType}", key, value, typeof(T).Name);
            }
        }

        return defaultValue;
    }

    /// <summary>
    /// 获取输入值
    /// </summary>
    /// <typeparam name="T">值类型</typeparam>
    /// <param name="inputData">输入数据</param>
    /// <param name="key">输入键</param>
    /// <param name="defaultValue">默认值</param>
    /// <returns>输入值</returns>
    protected T GetInputValue<T>(Dictionary<string, object> inputData, string key, T defaultValue = default!)
    {
        if (inputData.TryGetValue(key, out var value))
        {
            try
            {
                if (value is T directValue)
                    return directValue;

                return (T)Convert.ChangeType(value, typeof(T));
            }
            catch (Exception ex)
            {
                Logger.LogWarning(ex, "输入值转换失败 {Key}: {Value} -> {TargetType}", key, value, typeof(T).Name);
            }
        }

        return defaultValue;
    }

    /// <summary>
    /// 设置输出值
    /// </summary>
    /// <param name="outputData">输出数据</param>
    /// <param name="key">输出键</param>
    /// <param name="value">输出值</param>
    protected void SetOutputValue(Dictionary<string, object> outputData, string key, object? value)
    {
        if (value != null)
        {
            outputData[key] = value;
        }
    }

    /// <summary>
    /// 检查是否取消
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    protected void ThrowIfCancellationRequested(CancellationToken cancellationToken)
    {
        cancellationToken.ThrowIfCancellationRequested();
    }
}
